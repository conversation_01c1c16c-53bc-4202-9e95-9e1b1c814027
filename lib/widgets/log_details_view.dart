import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/log_entry.dart';
import 'message_content_view.dart';
// import 'raw_log_view.dart'; // Removed unused import
import 'package:collection/collection.dart';

// 定义一个回调函数类型，用于处理筛选操作
typedef OnFilter = void Function(String field, String value, bool isExclusion);

class LogDetailsView extends StatelessWidget {
  final LogEntry log;
  final OnFilter onFilter; // 新增回调
  final bool isMobile; // Add isMobile flag

  const LogDetailsView({
    super.key,
    required this.log,
    required this.onFilter, // 新增回调
    this.isMobile = false, // Default to false
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Column(
        children: [
          _buildJsonView(context),
          // const SizedBox(height: 8), // Removed as _buildRawLogView is removed
          // _buildRawLogView(context), // Removed unused method call
        ],
      )
    );
  }

  // 构建可交互的 JSON 树视图
  Widget _buildJsonView(BuildContext context) {
    if (!log.isJson) {
      return const SizedBox.shrink();
    }
    
    final parsedFields = log.parsedMessage;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade300),
      ),
      constraints: const BoxConstraints(maxHeight: 400),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (parsedFields.isNotEmpty) ...[
              _buildSectionHeader('关键信息 (从 message 解析)'),
              ..._buildJsonRows(parsedFields.map((k, v) => MapEntry(k, v as dynamic)), ''),
              const Divider(height: 16),
            ],
            _buildSectionHeader('Message Content'),
            _buildMessageContentView(),
          ]
        ),
      ),
    );
  }

  Widget _buildMessageContentView() {
    final message = log.parsed['message'] as String?;
    if (message == null) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: 8.0),
        child: Text('No message content.', style: TextStyle(fontStyle: FontStyle.italic, fontSize: 10)),
      );
    }

    const dataPrefix = 'data=';
    final dataIndex = message.indexOf(dataPrefix);

    if (dataIndex == -1) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
        child: SelectableText(message, style: const TextStyle(fontFamily: 'monospace', fontSize: 10)),
      );
    }
    
    final jsonString = message.substring(dataIndex + dataPrefix.length);
    
    try {
      final decodedJson = jsonDecode(jsonString);

      if (decodedJson is! Map<String, dynamic> || !decodedJson.containsKey('data')) {
        throw const FormatException('Expected "data" key in JSON');
      }
      final dataMap = decodedJson['data'];
      
      if (dataMap is! Map<String, dynamic> || !dataMap.containsKey('content')) {
         throw const FormatException('Expected "content" key in data');
      }
      final content = dataMap['content'];

      if (content is! List) {
        throw const FormatException('"content" is not a list');
      }
      
      return MessageContentView(content: content, isMobile: isMobile); // Pass isMobile here

    } catch (e) {
      // If anything goes wrong, display the raw message string for debugging.
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
        child: SelectableText(
          'Could not parse message content. Error: $e\\n\\n$message',
          style: const TextStyle(fontFamily: 'monospace', fontSize: 10, color: Colors.red),
        ),
      );
    }
  }

  // 递归构建 JSON 的每一行
  List<Widget> _buildJsonRows(Map<String, dynamic> json, String prefix, {Set<String> excludeKeys = const {}}) {
    final rows = <Widget>[];
    json.forEach((key, value) {
      if (excludeKeys.contains(key)) return;

      final fullKey = prefix.isEmpty ? key : '$prefix.$key';
      if (value is Map<String, dynamic>) {
        rows.add(
          Padding(
            padding: const EdgeInsets.only(left: 12.0, top: 4.0),
            child: Text('$key:', style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 10)),
          )
        );
        rows.addAll(_buildJsonRows(value, fullKey, excludeKeys: {})); // Do not pass excludeKeys down for nested objects
      } else {
        rows.add(_buildKeyValueRow(fullKey, value));
      }
    });
    return rows;
  }

  // 构建单独的键值对行
  Widget _buildKeyValueRow(String key, dynamic value) {
    final valueString = value.toString();
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {}, // For hover effect
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$key: ',
                style: const TextStyle(
                  color: Color(0xFF6A1B9A), // Deep purple for key
                  fontWeight: FontWeight.w500,
                  fontFamily: 'monospace',
                  fontSize: 10,
                ),
              ),
              Expanded(
                child: Text(
                  valueString,
                  style: const TextStyle(
                    color: Color(0xFF1E88E5), // Blue for value
                    fontFamily: 'monospace',
                    fontSize: 10,
                  ),
                ),
              ),
              _FilterButton(icon: Icons.add_circle_outline, onPressed: () => onFilter(key, valueString, false)),
              const SizedBox(width: 4),
              _FilterButton(icon: Icons.remove_circle_outline, onPressed: () => onFilter(key, valueString, true)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0, bottom: 4.0),
      child: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 11,
          color: Colors.grey.shade700
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text, String type) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$type 已复制到剪贴板'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  // Removed _buildRawLogView method
}

// Helper widget for filter buttons
class _FilterButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;

  const _FilterButton({required this.icon, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(2.0),
        child: Icon(icon, size: 14, color: Colors.grey.shade500),
      ),
    );
  }
}