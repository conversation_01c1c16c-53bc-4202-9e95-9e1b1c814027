import 'package:flutter/foundation.dart';

@immutable
class Filter {
  final String key;
  final String operator; // e.g., ":", ":>", ":<", ":>=", ":<="
  final String value;
  final bool isActive;

  const Filter({
    required this.key,
    required this.operator,
    required this.value,
    this.isActive = true,
  });

  Filter copyWith({
    String? key,
    String? operator,
    String? value,
    bool? isActive,
  }) {
    return Filter(
      key: key ?? this.key,
      operator: operator ?? this.operator,
      value: value ?? this.value,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() => 'Filter(key: $key, operator: $operator, value: $value, isActive: $isActive)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Filter &&
        other.key == key &&
        other.operator == operator &&
        other.value == value;
  }

  @override
  int get hashCode => key.hashCode ^ operator.hashCode ^ value.hashCode;
} 