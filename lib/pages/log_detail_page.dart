import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:tpa_log_reader/models/log_entry.dart';
import 'package:tpa_log_reader/widgets/message_content_view.dart';
import 'package:tpa_log_reader/utils/formatters.dart';

class LogDetailPage extends StatefulWidget {
  final LogEntry logEntry;

  const LogDetailPage({
    super.key,
    required this.logEntry,
  });

  @override
  State<LogDetailPage> createState() => _LogDetailPageState();
}

class _LogDetailPageState extends State<LogDetailPage> {
  List<Map<String, dynamic>>? _logEntries;
  List<Map<String, dynamic>>? _filteredLogEntries;
  
  // 日志类型过滤 - 对标压缩包分析器
  String? _selectedLogType = '错误'; // 默认选择错误日志
  final List<String> _logTypes = [
    '全部类型',
    '设备信息',
    '步骤',
    '指令',
    '日志',
    '错误'
  ];
  
  // 日志排序相关状态
  bool _isReversed = false;

  @override
  void initState() {
    super.initState();
    // _selectedLogType 已经在声明时设置为 '错误'，这里不需要重新设置
    _parseLogEntry();
  }

  void _parseLogEntry() {
    // 解析message中的data.content数组，提取实际的日志条目
    final List<Map<String, dynamic>> entries = [];

    try {
      final message = widget.logEntry.message;
      const dataPrefix = 'data=';
      final dataIndex = message.indexOf(dataPrefix);

      if (dataIndex != -1) {
        final jsonString = message.substring(dataIndex + dataPrefix.length);
        final decodedJson = json.decode(jsonString);

        if (decodedJson is Map<String, dynamic> &&
            decodedJson.containsKey('data') &&
            decodedJson['data'] is Map<String, dynamic> &&
            decodedJson['data'].containsKey('content')) {

          final content = decodedJson['data']['content'];
          if (content is List) {
            // 将content数组中的每个条目添加到entries中
            for (int i = 0; i < content.length; i++) {
              final item = content[i];
              if (item is Map<String, dynamic>) {
                // 添加索引信息以便排序和显示
                final entryWithIndex = Map<String, dynamic>.from(item);
                entryWithIndex['_index'] = i;
                entries.add(entryWithIndex);
              }
            }
          }
        }
      }

      // 如果没有找到content数组，创建一个默认条目
      if (entries.isEmpty) {
        entries.add({
          'type': 'unknown',
          'info': '无法解析日志内容',
          'date': DateTime.now().millisecondsSinceEpoch,
          '_index': 0,
        });
      }
    } catch (e) {
      print('解析日志条目时发生错误: $e');
      // 创建一个错误条目
      entries.add({
        'type': 'error',
        'info': '解析日志时发生错误: $e',
        'date': DateTime.now().millisecondsSinceEpoch,
        '_index': 0,
      });
    }

    setState(() {
      _logEntries = entries;
      _filteredLogEntries = entries;
    });

    _filterLogs();
  }

  // 判断日志是否为错误日志 - 针对data.content数组中的单个日志条目
  bool _isErrorLog(Map<String, dynamic> log) {
    // 检查type字段，参考压缩包分析器的逻辑
    final type = log['type'];
    final info = log['info'];

    // type 3 是日志类型，检查info字段是否包含错误信息
    if (type == 3 && info != null) {
      final infoStr = info.toString().toLowerCase();
      if (infoStr.contains('err') || infoStr.contains('error') ||
          infoStr.contains('异常') || infoStr.contains('错误')) {
        return true;
      }
    }

    return false;
  }

  // 过滤日志
  void _filterLogs() {
    if (_logEntries == null) return;
    
    setState(() {
      if (_selectedLogType == null || _selectedLogType == '全部类型') {
        _filteredLogEntries = _logEntries;
      } else {
        _filteredLogEntries = _logEntries!.where((log) {
          // 类型过滤，完全对标压缩包分析器
          final logType = log['type'];
          switch (_selectedLogType) {
            case '设备信息':
              return logType == 0;
            case '步骤':
              return logType == 1 && !_isErrorLog(log); // 排除错误日志
            case '指令':
              return logType == 2 && !_isErrorLog(log); // 排除错误日志
            case '日志':
              return logType == 3 && !_isErrorLog(log); // 排除错误日志
            case '错误':
              return _isErrorLog(log); // 使用统一的错误判断逻辑
            default:
              return true;
          }
        }).toList();
      }
      
      // 应用排序
      if (_isReversed && _filteredLogEntries != null) {
        _filteredLogEntries = _filteredLogEntries!.reversed.toList();
      }
    });
  }

  // 切换日志排序顺序
  void _toggleLogOrder() {
    setState(() {
      _isReversed = !_isReversed;
      if (_filteredLogEntries != null) {
        _filteredLogEntries = _filteredLogEntries!.reversed.toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final timeData = formatTimeForTimeline(widget.logEntry.timestamp);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('日志详情 - ${timeData['date']} ${timeData['time']}'),
        // actions: [
        //   IconButton(
        //     icon: Icon(_isReversed ? Icons.arrow_upward : Icons.arrow_downward),
        //     tooltip: _isReversed ? '最新在上' : '最旧在上',
        //     onPressed: _toggleLogOrder,
        //   ),
        // ],
      ),
      backgroundColor: const Color(0xFFF8F9FA),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_logEntries == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 筛选卡片 - 显示基本信息
          _buildFiltersCard(),
          const SizedBox(height: 16),
          // 日志内容 - 与压缩包分析器完全一致的布局
          _buildLogsView(),
        ],
      ),
    );
  }

  Widget _buildFiltersCard() {
    final timeData = formatTimeForTimeline(widget.logEntry.timestamp);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('日志详情', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 12),
            Wrap(
              spacing: 24.0,
              runSpacing: 8.0,
              children: [
                _buildInfoItem('时间', '${timeData['date']} ${timeData['time']}'),
                _buildInfoItem('来源', widget.logEntry.domain),
                _buildInfoItem('级别', widget.logEntry.level),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogsView() {
    if (_logEntries == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: Center(child: Text('没有可显示的日志。'))
        )
      );
    }

    return Card(
      child: Column(
        children: [
          // 添加日志统计信息 - 与压缩包分析器完全一致
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // 检测是否为移动端（宽度小于600px）
                final isMobile = constraints.maxWidth < 600;
                final totalLogs = _logEntries!.length;
                final filteredLogs = _filteredLogEntries!.length;
                final errorLogs = _filteredLogEntries!.where(_isErrorLog).length;

                if (isMobile) {
                  // 移动端使用垂直布局
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 第一行：统计信息
                      Row(
                        children: [
                          const Icon(Icons.info_outline, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '共 $filteredLogs/$totalLogs 条日志',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '错误: $errorLogs 条',
                            style: const TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // 第二行：控制按钮
                      Row(
                        children: [
                          // 日志类型过滤
                          Expanded(
                            child: DropdownButton<String>(
                              value: _selectedLogType,
                              items: _logTypes.map((type) =>
                                DropdownMenuItem(value: type, child: Text(type))
                              ).toList(),
                              onChanged: (type) {
                                setState(() {
                                  _selectedLogType = type;
                                });
                                _filterLogs();
                              },
                              underline: Container(height: 1, color: Colors.grey.shade300),
                              isExpanded: true,
                            ),
                          ),
                          const SizedBox(width: 16),
                          TextButton.icon(
                            icon: Icon(_isReversed ? Icons.arrow_downward : Icons.arrow_upward, size: 16),
                            label: Text(_isReversed ? '倒序' : '顺序'),
                            onPressed: _toggleLogOrder,
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                } else {
                  // 桌面端使用水平布局
                  return Row(
                    children: [
                      const Icon(Icons.info_outline, size: 16),
                      const SizedBox(width: 8),
                      Text('共 $filteredLogs/$totalLogs 条日志',
                        style: const TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(width: 16),
                      Text('错误: $errorLogs 条',
                        style: const TextStyle(color: Colors.red)),
                      const Spacer(),
                      // 日志类型过滤
                      DropdownButton<String>(
                        value: _selectedLogType,
                        items: _logTypes.map((type) =>
                          DropdownMenuItem(value: type, child: Text(type))
                        ).toList(),
                        onChanged: (type) {
                          setState(() {
                            _selectedLogType = type;
                          });
                          _filterLogs();
                        },
                        underline: Container(height: 1, color: Colors.grey.shade300),
                      ),
                      const SizedBox(width: 16),
                      TextButton.icon(
                        icon: Icon(_isReversed ? Icons.arrow_downward : Icons.arrow_upward),
                        label: Text(_isReversed ? '倒序' : '顺序'),
                        onPressed: _toggleLogOrder,
                      ),
                    ],
                  );
                }
              },
            ),
          ),
          const Divider(height: 1),
          // 日志内容
          MessageContentView(
            content: _filteredLogEntries!,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return RichText(
      text: TextSpan(
        style: Theme.of(context).textTheme.bodyMedium,
        children: [
          TextSpan(
            text: '$label: ',
            style: const TextStyle(color: Colors.grey),
          ),
          TextSpan(
            text: value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }


}
