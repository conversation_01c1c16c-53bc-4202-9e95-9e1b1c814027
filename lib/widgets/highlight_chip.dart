import 'package:flutter/material.dart';

class HighlightChip extends StatelessWidget {
  final String fieldName;
  final String highlightedValue;

  const HighlightChip({
    super.key,
    required this.fieldName,
    required this.highlightedValue,
  });

  @override
  Widget build(BuildContext context) {
    // Elasticsearch highlight aagTags
    const preTag = '@kibana-highlighted-field@';
    const postTag = '@/kibana-highlighted-field@';

    final textSpans = <TextSpan>[];
    final parts = highlightedValue.split(preTag);

    for (int i = 0; i < parts.length; i++) {
      final part = parts[i];
      if (part.isEmpty) continue;

      if (i > 0) { // This part was wrapped in preTag
        final subParts = part.split(postTag);
        if (subParts.length > 1) {
          textSpans.add(
            TextSpan(
              text: subParts[0],
              style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
            ),
          );
          if (subParts[1].isNotEmpty) {
            textSpans.add(TextSpan(text: subParts[1]));
          }
        } else {
           textSpans.add(TextSpan(text: part));
        }
      } else {
        textSpans.add(TextSpan(text: part));
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 4, right: 6),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.amber.shade100,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.amber.shade300, width: 0.5)
      ),
      child: RichText(
        text: TextSpan(
          style: DefaultTextStyle.of(context).style.copyWith(fontSize: 12),
          children: [
            TextSpan(
              text: '$fieldName: ',
              style: TextStyle(color: Colors.grey.shade700, fontWeight: FontWeight.w500),
            ),
            ...textSpans,
          ],
        ),
      ),
    );
  }
} 