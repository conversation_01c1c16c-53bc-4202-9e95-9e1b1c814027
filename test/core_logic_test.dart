import 'package:test/test.dart';
import 'dart:convert';

void main() {
  group('核心逻辑测试', () {
    group('消息解析测试', () {
      test('应该正确解析email字段', () {
        const message = 'report task log, email=<EMAIL>, data={"data":{"taskId":"5a57d82c144b49c98345fbe5469fadc8"}}';
        
        // 模拟LogEntry的解析逻辑
        final parsedMessage = _parseMessage(message);
        
        // 验证email是否被正确解析
        expect(parsedMessage['email'], equals('<EMAIL>'));
        print('✓ Email解析测试通过: ${parsedMessage['email']}');
      });

      test('应该正确解析device信息', () {
        const message = 'report task log, email=<EMAIL>, data={"data":{"taskId":"123","device":{"appVersion":"1.3.2","pythonVersion":"1.1.1","scriptVersion":"1.5.23","platformName":"macos","systemArchs":"X86_64","systemVersion":"15.5","systemName":"macOS","systemBitness":"64"}}}';
        
        final parsedMessage = _parseMessage(message);
        
        // 验证device信息是否被正确解析
        expect(parsedMessage['appVersion'], equals('1.3.2'));
        expect(parsedMessage['platformName'], equals('macos'));
        expect(parsedMessage['systemName'], equals('macOS'));
        expect(parsedMessage['taskId'], equals('123'));
        
        print('✓ Device信息解析测试通过:');
        print('  - appVersion: ${parsedMessage['appVersion']}');
        print('  - platformName: ${parsedMessage['platformName']}');
        print('  - systemName: ${parsedMessage['systemName']}');
        print('  - taskId: ${parsedMessage['taskId']}');
      });

      test('应该正确处理不完整的消息', () {
        const message = 'report task log, email=<EMAIL>';
        
        final parsedMessage = _parseMessage(message);
        
        expect(parsedMessage['email'], equals('<EMAIL>'));
        expect(parsedMessage['taskId'], isNull);
        
        print('✓ 不完整消息解析测试通过');
      });
    });

    group('查询构建测试', () {
      test('应该为email字段构建正确的查询', () {
        const key = 'email';
        const value = '<EMAIL>';
        
        // 模拟KibanaService的查询构建逻辑
        final query = _buildSpecialMessageQuery(key, value);
        
        final expectedQuery = {
          "match_phrase": {
            "message": {
              "query": "email=<EMAIL>",
            }
          }
        };
        
        expect(query, equals(expectedQuery));
        print('✓ Email查询构建测试通过');
        print('  查询: ${jsonEncode(query)}');
      });

      test('应该为platformName字段构建正确的查询', () {
        const key = 'platformName';
        const value = 'macos';
        
        final query = _buildSpecialMessageQuery(key, value);
        
        final expectedQuery = {
          "match_phrase": {
            "message": {
              "query": '"platformName":"macos"',
            }
          }
        };
        
        expect(query, equals(expectedQuery));
        print('✓ PlatformName查询构建测试通过');
        print('  查询: ${jsonEncode(query)}');
      });

      test('应该为其他特殊字段构建正确的查询', () {
        const testCases = [
          {'key': 'taskId', 'value': '123', 'expected': '"taskId":"123"'},
          {'key': 'appVersion', 'value': '1.3.2', 'expected': '"appVersion":"1.3.2"'},
          {'key': 'systemName', 'value': 'macOS', 'expected': '"systemName":"macOS"'},
        ];
        
        for (final testCase in testCases) {
          final query = _buildSpecialMessageQuery(testCase['key']!, testCase['value']!);
          final expectedQuery = {
            "match_phrase": {
              "message": {
                "query": testCase['expected'],
              }
            }
          };
          
          expect(query, equals(expectedQuery));
          print('✓ ${testCase['key']}查询构建测试通过');
        }
      });
    });

    group('搜索逻辑验证测试', () {
      test('验证email搜索逻辑', () {
        // 模拟一个包含email的日志消息
        const logMessage = 'report task log, email=<EMAIL>, data={"data":{"taskId":"abc123"}}';
        const searchEmail = '<EMAIL>';
        
        // 验证搜索逻辑是否能匹配
        final shouldMatch = _messageContainsEmailSearch(logMessage, searchEmail);
        expect(shouldMatch, isTrue);
        
        // 验证不匹配的情况
        const wrongEmail = '<EMAIL>';
        final shouldNotMatch = _messageContainsEmailSearch(logMessage, wrongEmail);
        expect(shouldNotMatch, isFalse);
        
        print('✓ Email搜索逻辑验证测试通过');
      });

      test('验证platformName搜索逻辑', () {
        const logMessage = 'report task log, email=<EMAIL>, data={"data":{"taskId":"123","device":{"platformName":"macos"}}}';
        const searchPlatform = 'macos';
        
        final shouldMatch = _messageContainsPlatformSearch(logMessage, searchPlatform);
        expect(shouldMatch, isTrue);
        
        const wrongPlatform = 'windows';
        final shouldNotMatch = _messageContainsPlatformSearch(logMessage, wrongPlatform);
        expect(shouldNotMatch, isFalse);
        
        print('✓ PlatformName搜索逻辑验证测试通过');
      });
    });
  });
}

// 模拟LogEntry的_parseMessage方法
Map<String, dynamic> _parseMessage(String message) {
  final result = <String, dynamic>{};
  
  // 解析email
  final emailMatch = RegExp(r'email=([\w.-]+@[\w.-]+)').firstMatch(message);
  if (emailMatch != null) {
    result['email'] = emailMatch.group(1);
  }
  
  // 解析data部分的JSON
  final dataMatch = RegExp(r'data=(\{.*\})').firstMatch(message);
  if (dataMatch != null) {
    try {
      final dataJson = jsonDecode(dataMatch.group(1)!);
      if (dataJson is Map<String, dynamic>) {
        final data = dataJson['data'];
        if (data is Map<String, dynamic>) {
          // 提取taskId
          if (data.containsKey('taskId')) {
            result['taskId'] = data['taskId'];
          }
          
          // 提取device信息
          if (data.containsKey('device') && data['device'] is Map<String, dynamic>) {
            final device = data['device'] as Map<String, dynamic>;
            result.addAll(device);
          }
        }
      }
    } catch (e) {
      // JSON解析失败，忽略
    }
  }
  
  return result;
}

// 模拟KibanaService的特殊消息查询构建逻辑
Map<String, dynamic> _buildSpecialMessageQuery(String key, String value) {
  const specialMessageKeys = {
    'email', 'taskId', 'appVersion', 'pythonVersion', 'scriptVersion', 
    'platformName', 'systemArchs', 'systemVersion', 'systemName', 'systemBitness'
  };
  
  if (specialMessageKeys.contains(key)) {
    String queryString;
    if (key == 'email') {
      queryString = '$key=$value';
    } else {
      queryString = '"$key":"$value"';
    }
    
    return {
      "match_phrase": {
        "message": {
          "query": queryString,
        }
      }
    };
  }
  
  // 对于非特殊字段，返回普通查询
  return {
    "match": {
      key: value,
    }
  };
}

// 验证消息是否包含email搜索
bool _messageContainsEmailSearch(String message, String searchEmail) {
  return message.contains('email=$searchEmail');
}

// 验证消息是否包含platformName搜索
bool _messageContainsPlatformSearch(String message, String searchPlatform) {
  return message.contains('"platformName":"$searchPlatform"');
}
