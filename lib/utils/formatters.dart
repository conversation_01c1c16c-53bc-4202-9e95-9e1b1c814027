/// 格式化时间用于时间线显示。
/// 返回一个包含 'time' 和 'date' 的 Map。
Map<String, String> formatTimeForTimeline(String timestamp) {
  if (timestamp.isEmpty) return {'time': 'N/A', 'date': 'N/A'};

  try {
    DateTime dateTime;

    // 尝试不同的时间戳格式
    if (timestamp.contains('T')) {
      // ISO 8601 格式: 2024-12-31T10:30:45.123Z 或 2024-12-31T10:30:45.123+08:00
      dateTime = DateTime.parse(timestamp);
    } else if (timestamp.contains('-') && timestamp.contains(':')) {
      // 可能是没有T的格式: 2024-12-31 10:30:45.123
      dateTime = DateTime.parse(timestamp.replaceFirst(' ', 'T'));
    } else if (RegExp(r'^\d+$').hasMatch(timestamp)) {
      // 纯数字，可能是毫秒时间戳
      final millis = int.tryParse(timestamp);
      if (millis != null) {
        dateTime = DateTime.fromMillisecondsSinceEpoch(millis);
      } else {
        throw FormatException('Invalid timestamp format');
      }
    } else {
      // 尝试直接解析
      dateTime = DateTime.parse(timestamp);
    }

    // 转换为东八区时间（UTC+8）
    final east8DateTime = dateTime.toLocal();

    // 格式化输出
    final milliseconds = east8DateTime.millisecond.toString().padLeft(3, '0');
    return {
      'time':
          '${east8DateTime.hour.toString().padLeft(2, '0')}:${east8DateTime.minute.toString().padLeft(2, '0')}:${east8DateTime.second.toString().padLeft(2, '0')}.$milliseconds',
      'date':
          '${east8DateTime.year}-${east8DateTime.month.toString().padLeft(2, '0')}-${east8DateTime.day.toString().padLeft(2, '0')}'
    };
  } catch (e) {
    // 添加调试信息
    print('Error parsing timestamp "$timestamp": $e');
    return {'time': 'Invalid Time', 'date': 'Invalid Date'};
  }
}