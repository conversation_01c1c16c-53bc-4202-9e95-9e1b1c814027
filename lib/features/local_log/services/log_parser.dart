import 'dart:convert';
import 'dart:io';
import 'package:archive/archive_io.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import '../models/log_entry.dart';
import '../models/log_session.dart';

class LogParserService {
  Future<String> unzipLogArchive(String zipPath) async {
    final tempDir = await getTemporaryDirectory();
    final destinationDir = Directory(p.join(tempDir.path, 'tpa_log_reader_${DateTime.now().millisecondsSinceEpoch}'));
    if (await destinationDir.exists()) {
      await destinationDir.delete(recursive: true);
    }
    await destinationDir.create(recursive: true);

    try {
      await extractFileToDisk(zipPath, destinationDir.path);
    } catch (e) {
      throw Exception('解压文件失败: $e');
    }
    
    final entities = await destinationDir.list().toList();
    final contentDirs = entities
        .whereType<Directory>()
        .where((d) => !p.basename(d.path).startsWith('__'))
        .toList();

    if (contentDirs.length == 1) {
      return contentDirs.first.path;
    } else if (contentDirs.isNotEmpty) {
      final allAreDateDirs = contentDirs.every((d) => RegExp(r'^\d{4}-\d{1,2}-\d{1,2}$').hasMatch(p.basename(d.path)));
      if (allAreDateDirs) {
        return destinationDir.path;
      }
    }
    
    throw Exception('无法在压缩包中确定唯一的日志根目录。');
  }

  Future<Map<String, List<LogSession>>> scanDirectoryForSessions(String rootPath) async {
    final Map<String, List<LogSession>> sessionsByDate = {};
    final rootDir = Directory(rootPath);

    if (!await rootDir.exists()) {
      throw Exception("选择的目录不存在: $rootPath");
    }

    final dateEntities = await rootDir.list().toList();

    for (var dateEntity in dateEntities) {
      if (dateEntity is Directory) {
        final dateDirName = p.basename(dateEntity.path);
        final sessions = <LogSession>[];
        
        await for (var sessionEntity in dateEntity.list()) {
          if (sessionEntity is Directory) {
            final sessionPath = sessionEntity.path;
            final sessionName = p.basename(sessionPath);
            final logFile = File(p.join(sessionPath, 'log'));

            if (await logFile.exists()) {
              final imagePaths = <String>[];
              const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp'];
              await for (var file in sessionEntity.list()) {
                if (file is File) {
                  final extension = p.extension(file.path).toLowerCase();
                  if (imageExtensions.contains(extension)) {
                    imagePaths.add(file.path);
                  }
                }
              }

              final parts = sessionName.split('_');
              if (parts.length >= 4) {
                 final time = parts.sublist(parts.length - 3).join('_');
                 sessions.add(LogSession(
                   path: sessionPath,
                   date: dateDirName,
                   name: sessionName,
                   time: time,
                   imagePaths: imagePaths,
                 ));
              }
            }
          }
        }

        if (sessions.isNotEmpty) {
          // 使用自定义的时间比较函数，正确处理时分秒格式
          sessions.sort((a, b) => _compareTimeStrings(b.time, a.time));
          sessionsByDate[dateDirName] = sessions;
        }
      }
    }

    final sortedKeys = sessionsByDate.keys.toList()
      ..sort((a, b) => b.compareTo(a));

    return {for (var k in sortedKeys) k: sessionsByDate[k]!};
  }

  // 比较时间字符串，格式为 "HH_MM_SS"，正确处理单位数的情况
  int _compareTimeStrings(String timeA, String timeB) {
    try {
      // 将时间字符串转换为可比较的格式
      final partsA = timeA.split('_');
      final partsB = timeB.split('_');

      if (partsA.length != 3 || partsB.length != 3) {
        // 如果格式不正确，回退到字符串比较
        return timeA.compareTo(timeB);
      }

      // 解析小时、分钟、秒
      final hourA = int.tryParse(partsA[0]) ?? 0;
      final minuteA = int.tryParse(partsA[1]) ?? 0;
      final secondA = int.tryParse(partsA[2]) ?? 0;

      final hourB = int.tryParse(partsB[0]) ?? 0;
      final minuteB = int.tryParse(partsB[1]) ?? 0;
      final secondB = int.tryParse(partsB[2]) ?? 0;

      // 转换为总秒数进行比较
      final totalSecondsA = hourA * 3600 + minuteA * 60 + secondA;
      final totalSecondsB = hourB * 3600 + minuteB * 60 + secondB;

      return totalSecondsA.compareTo(totalSecondsB);
    } catch (e) {
      // 解析失败时回退到字符串比较
      return timeA.compareTo(timeB);
    }
  }

  Future<List<Map<String, dynamic>>> parseLogFile(String sessionPath) async {
    final logFile = File(p.join(sessionPath, 'log'));
    if (!await logFile.exists()) {
      throw Exception("Log 文件不存在于: $sessionPath");
    }

    final List<Map<String, dynamic>> entries = [];
    final lines = logFile.openRead().transform(utf8.decoder).transform(const LineSplitter());

    await for (var line in lines) {
      if (line.trim().isNotEmpty) {
        try {
          final json = jsonDecode(line);
          if (json is Map<String, dynamic>) {
            entries.add(json);
          }
        } catch (e) {
          // Ignore lines that are not valid JSON
        }
      }
    }
    return entries;
  }
}
