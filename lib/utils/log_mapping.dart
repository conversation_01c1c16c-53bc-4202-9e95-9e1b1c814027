// lib/utils/log_mapping.dart

const Map<String, String> stepToText = {
  "1": "任务启动",
  "2": "数据初始化",
  "3": "脚本初始化",
  "4": "开始日志存储",
  "5": "配置设置",
  "9": "加载数据",
  "10": "反序列化数据",
  "15": "激活企微",
  "16": "调整企微大小",
  "20": "隐藏侧边栏",
  "22": "切换至消息标签",
  "28": "修正全局搜索框位置及大小",
  "29": "服务已启动，开始链接",
  "30": "执行消息发送",
  "34": "开始链接企微应用",
  "38": "关闭企微应用链接",
  "40": "寻找聊天目标",
  "41": "搜索发送目标",
  "42": "焦点至聊天框",
  "49": "完成目标操作",
  "60": "开始消息发送",
  "69": "完成消息发送",
  "90": "激活教师精灵",
  "100": "脚本完成退出",
  "101": "脚本异常退出",
};

const Map<String, String> cmdToText = {
  "1": "激活指定应用",
  "2": "调整企微窗口大小",
  "10": "鼠标左键点击",
  "11": "鼠标右键点击",
  "12": "鼠标移动",
  "20": "键盘键入",
  "60": "打开文件",
  "99": "休眠"
};

// 优化后的日志类型映射，按功能领域分类
const Map<String, String> logToText = {
  // 设备与环境信息
  "resolution": "屏幕分辨率",
  "screenedge": "屏幕边界",
  "scale": "屏幕缩放率",
  "info": "系统信息",
  "position": "元素位置",
  "wxrect": "企微窗口信息",
  "searchrect": "搜索框信息",
  "rotate180": "屏幕旋转",
  
  // 错误与异常
  "err": "错误",
  "unmatch": "图像匹配失败",
  "ocr": "文字识别失败",
  "open_h5_application_fail": "应用启动失败",
  
  // 连接与通信
  "connected_server": "服务器连接",
  "connected_h5": "应用连接状态",
  
  // 任务与流程
  "taskdata": "任务数据",
  "opened_h5_application": "应用已启动",
  "h5_switch_user_success": "用户切换成功",
  "check_and_focus_h5_switch_user_success": "用户验证成功",
  "top_window": "窗口层级异常",
  
  // 新增类型
  "ui_action": "界面操作",
  "network": "网络请求",
  "auth": "认证操作",
  "file": "文件操作",
  "performance": "性能数据",
  "user_action": "用户操作",
  "system": "系统事件"
};

const Map<int, String> errorCodeToText = {
  300: "任务默认错误",
  310: "企微没启动",
  311: "图片不匹配",
  312: "OCR不识别",
  313: "企微没登录",
  314: "找不到任何企微窗口",
  315: "企微版本号低于4.1.20",
  320: "缩放率或者屏幕不支持",
  330: "入参的文件有问题",
  331: "入参文件被篡改",
  350: "无法与Flutter建立连接",
  351: "无法与H5建立连接",
  390: "用户取消中断",
  200: "聊天默认错误",
  201: "企微全局搜索，找不到对应的人",
  202: "打开的聊天窗口和要发送的对象名称不一致",
  203: "搜索到的item，第一个是创建群聊",
  204: "chat name 为空或者空串",
  210: "搜索框旁边的【创建群聊】入口图标没找到",
  211: "全局搜索【联系人】或【群聊】图片没找到",
  212: "聊天窗口emoj图标没找到",
  220: "企微全局搜索框没找到",
  221: "企微全局搜索框搜索结果没出现",
  222: "企微全局搜索框菜单匹配不对",
  230: "拷贝聊天名称失败",
  250: "H5没有找到用户",
  251: "H5找人不对",
  100: "消息默认错误",
  110: "拷贝到的文件，和应该发送的文件不匹配",
  111: "要发送的文件找不到",
  120: "剪切板复制失败",
  121: "剪切板异常",
  122: "剪切板复制文件失败",
  401: "打开企微工作台页面失败"
}; 