#!/bin/bash

# TPA Log Reader - 全平台构建脚本
# 用于自动构建 Android APK 和 macOS 应用

set -e  # 遇到错误时退出

echo "🚀 开始构建 TPA Log Reader..."
echo "================================"

# 检查 Flutter 环境
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter 未安装或未添加到 PATH"
    echo "请先安装 Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
fi

echo "✅ Flutter 环境检查通过"
flutter --version

# 清理之前的构建
echo ""
echo "🧹 清理之前的构建..."
flutter clean
flutter pub get

# 构建 Android APK
echo ""
echo "📱 开始构建 Android APK..."
echo "--------------------------------"
flutter build apk --release

if [ $? -eq 0 ]; then
    APK_SIZE=$(du -h build/app/outputs/flutter-apk/app-release.apk | cut -f1)
    echo "✅ Android APK 构建成功!"
    echo "📦 文件位置: build/app/outputs/flutter-apk/app-release.apk"
    echo "📏 文件大小: $APK_SIZE"
else
    echo "❌ Android APK 构建失败"
    exit 1
fi

# 构建 macOS 应用 (仅在 macOS 上)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo ""
    echo "🖥️  开始构建 macOS 应用..."
    echo "--------------------------------"
    flutter build macos --release
    
    if [ $? -eq 0 ]; then
        APP_SIZE=$(du -sh build/macos/Build/Products/Release/tpa_log_reader.app | cut -f1)
        echo "✅ macOS 应用构建成功!"
        echo "📦 文件位置: build/macos/Build/Products/Release/tpa_log_reader.app"
        echo "📏 文件大小: $APP_SIZE"
    else
        echo "❌ macOS 应用构建失败"
        exit 1
    fi
else
    echo "⚠️  跳过 macOS 构建 (当前系统不是 macOS)"
fi

# 构建总结
echo ""
echo "🎉 构建完成!"
echo "================================"
echo "📱 Android APK: build/app/outputs/flutter-apk/app-release.apk"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🖥️  macOS App: build/macos/Build/Products/Release/tpa_log_reader.app"
fi
echo ""
echo "💡 提示:"
echo "- Android APK 可直接安装到 Android 设备"
echo "- macOS 应用可直接运行或拖拽到应用程序文件夹"
echo "- 如需分发，请考虑代码签名和公证"
