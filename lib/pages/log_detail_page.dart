import 'package:flutter/material.dart';
import 'package:tpa_log_reader/models/log_entry.dart';
import 'package:tpa_log_reader/widgets/message_content_view.dart';
import 'package:tpa_log_reader/utils/formatters.dart';

class LogDetailPage extends StatefulWidget {
  final LogEntry logEntry;

  const LogDetailPage({
    super.key,
    required this.logEntry,
  });

  @override
  State<LogDetailPage> createState() => _LogDetailPageState();
}

class _LogDetailPageState extends State<LogDetailPage> {
  List<Map<String, dynamic>>? _logEntries;
  List<Map<String, dynamic>>? _filteredLogEntries;
  
  // 日志类型过滤 - 对标压缩包分析器
  String? _selectedLogType = '错误'; // 默认选择错误日志
  final List<String> _logTypes = [
    '全部类型',
    '普通日志',
    '错误'
  ];
  
  // 日志排序相关状态
  bool _isReversed = false;

  @override
  void initState() {
    super.initState();
    _selectedLogType = '全部类型';
    _parseLogEntry();
  }

  void _parseLogEntry() {
    // 将单个日志条目转换为MessageContentView可以处理的格式
    final List<Map<String, dynamic>> entries = [];
    
    // 添加主要日志条目
    final mainEntry = {
      'timestamp': widget.logEntry.timestamp,
      'message': widget.logEntry.message,
      'level': widget.logEntry.level,
      'domain': widget.logEntry.domain,
      'parsedMessage': widget.logEntry.parsedMessage,
      'type': 'main',
    };
    entries.add(mainEntry);

    // 如果有解析的字段，为每个字段创建一个条目
    if (widget.logEntry.parsedMessage.isNotEmpty) {
      widget.logEntry.parsedMessage.forEach((key, value) {
        entries.add({
          'timestamp': widget.logEntry.timestamp,
          'message': '$key: $value',
          'level': 'info',
          'domain': 'parsed_field',
          'type': 'field',
          'field_key': key,
          'field_value': value,
        });
      });
    }
    
    setState(() {
      _logEntries = entries;
      _filteredLogEntries = entries;
    });
    
    _filterLogs();
  }

  // 判断日志是否为错误日志 - 针对Kibana日志数据
  bool _isErrorLog(Map<String, dynamic> log) {
    // 检查日志级别
    final level = log['level']?.toString().toLowerCase() ?? '';
    if (level.contains('error') || level.contains('err') || level.contains('fatal')) {
      return true;
    }

    // 检查消息内容中的错误关键词
    final message = log['message']?.toString().toLowerCase() ?? '';
    if (message.contains('error') ||
        message.contains('exception') ||
        message.contains('fail') ||
        message.contains('错误') ||
        message.contains('异常')) {
      return true;
    }

    // 检查解析字段中的错误信息
    final parsedMessage = log['parsedMessage'] as Map<String, String>? ?? {};
    for (final entry in parsedMessage.entries) {
      final keyLower = entry.key.toLowerCase();
      final valueLower = entry.value.toLowerCase();

      if (keyLower.contains('error') || keyLower.contains('err') ||
          valueLower.contains('error') || valueLower.contains('exception') ||
          valueLower.contains('fail') || valueLower.contains('错误') ||
          valueLower.contains('异常')) {
        return true;
      }
    }

    return false;
  }

  // 过滤日志
  void _filterLogs() {
    if (_logEntries == null) return;
    
    setState(() {
      if (_selectedLogType == null || _selectedLogType == '全部类型') {
        _filteredLogEntries = _logEntries;
      } else {
        _filteredLogEntries = _logEntries!.where((log) {
          switch (_selectedLogType) {
            case '错误':
              return _isErrorLog(log);
            case '普通日志':
              return !_isErrorLog(log);
            default:
              return true;
          }
        }).toList();
      }
      
      // 应用排序
      if (_isReversed && _filteredLogEntries != null) {
        _filteredLogEntries = _filteredLogEntries!.reversed.toList();
      }
    });
  }

  // 切换日志排序顺序
  void _toggleLogOrder() {
    setState(() {
      _isReversed = !_isReversed;
      if (_filteredLogEntries != null) {
        _filteredLogEntries = _filteredLogEntries!.reversed.toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final timeData = formatTimeForTimeline(widget.logEntry.timestamp);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('日志详情 - ${timeData['date']} ${timeData['time']}'),
        actions: [
          IconButton(
            icon: Icon(_isReversed ? Icons.arrow_upward : Icons.arrow_downward),
            tooltip: _isReversed ? '最新在上' : '最旧在上',
            onPressed: _toggleLogOrder,
          ),
        ],
      ),
      backgroundColor: const Color(0xFFF8F9FA),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_logEntries == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 筛选卡片 - 显示基本信息
          _buildFiltersCard(),
          const SizedBox(height: 16),
          // 日志内容 - 与压缩包分析器完全一致的布局
          _buildLogsView(),
        ],
      ),
    );
  }

  Widget _buildFiltersCard() {
    final timeData = formatTimeForTimeline(widget.logEntry.timestamp);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('日志详情', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 12),
            Wrap(
              spacing: 24.0,
              runSpacing: 8.0,
              children: [
                _buildInfoItem('时间', '${timeData['date']} ${timeData['time']}'),
                _buildInfoItem('来源', widget.logEntry.domain),
                _buildInfoItem('级别', widget.logEntry.level),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogsView() {
    if (_logEntries == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: Center(child: Text('没有可显示的日志。'))
        )
      );
    }

    return Card(
      child: Column(
        children: [
          // 添加日志统计信息 - 与压缩包分析器完全一致
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // 检测是否为移动端（宽度小于600px）
                final isMobile = constraints.maxWidth < 600;
                final totalLogs = _logEntries!.length;
                final filteredLogs = _filteredLogEntries!.length;
                final errorLogs = _filteredLogEntries!.where(_isErrorLog).length;

                if (isMobile) {
                  // 移动端使用垂直布局
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 第一行：统计信息
                      Row(
                        children: [
                          const Icon(Icons.info_outline, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '共 $filteredLogs/$totalLogs 条日志',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '错误: $errorLogs 条',
                            style: const TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // 第二行：控制按钮
                      Row(
                        children: [
                          // 日志类型过滤
                          Expanded(
                            child: DropdownButton<String>(
                              value: _selectedLogType,
                              items: _logTypes.map((type) =>
                                DropdownMenuItem(value: type, child: Text(type))
                              ).toList(),
                              onChanged: (type) {
                                setState(() {
                                  _selectedLogType = type;
                                });
                                _filterLogs();
                              },
                              underline: Container(height: 1, color: Colors.grey.shade300),
                              isExpanded: true,
                            ),
                          ),
                          const SizedBox(width: 16),
                          TextButton.icon(
                            icon: Icon(_isReversed ? Icons.arrow_downward : Icons.arrow_upward, size: 16),
                            label: Text(_isReversed ? '倒序' : '顺序'),
                            onPressed: _toggleLogOrder,
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                } else {
                  // 桌面端使用水平布局
                  return Row(
                    children: [
                      const Icon(Icons.info_outline, size: 16),
                      const SizedBox(width: 8),
                      Text('共 $filteredLogs/$totalLogs 条日志',
                        style: const TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(width: 16),
                      Text('错误: $errorLogs 条',
                        style: const TextStyle(color: Colors.red)),
                      const Spacer(),
                      // 日志类型过滤
                      DropdownButton<String>(
                        value: _selectedLogType,
                        items: _logTypes.map((type) =>
                          DropdownMenuItem(value: type, child: Text(type))
                        ).toList(),
                        onChanged: (type) {
                          setState(() {
                            _selectedLogType = type;
                          });
                          _filterLogs();
                        },
                        underline: Container(height: 1, color: Colors.grey.shade300),
                      ),
                      const SizedBox(width: 16),
                      TextButton.icon(
                        icon: Icon(_isReversed ? Icons.arrow_downward : Icons.arrow_upward),
                        label: Text(_isReversed ? '倒序' : '顺序'),
                        onPressed: _toggleLogOrder,
                      ),
                    ],
                  );
                }
              },
            ),
          ),
          const Divider(height: 1),
          // 日志内容
          MessageContentView(
            content: _filteredLogEntries!,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return RichText(
      text: TextSpan(
        style: Theme.of(context).textTheme.bodyMedium,
        children: [
          TextSpan(
            text: '$label: ',
            style: const TextStyle(color: Colors.grey),
          ),
          TextSpan(
            text: value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }


}
