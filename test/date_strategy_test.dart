import 'package:flutter_test/flutter_test.dart';
import 'package:tpa_log_reader/kibana_service.dart';

/// 日期分段查询策略测试
/// 
/// 测试三种不同的时间段查询策略：
/// 1. 2025.6.27及以后 - 使用新接口 (n9elol.staff.xdf.cn)
/// 2. 2025.6.25-26 - 使用p1-weixinshengtai大索引 (kibanalb.staff.xdf.cn)
/// 3. 2025.6.25之前 - 使用用户选择的索引 (kibanalb.staff.xdf.cn)
void main() {
  group('日期分段查询策略测试', () {
    late KibanaService kibanaService;

    setUp(() {
      kibanaService = KibanaService();
    });

    /// 测试1: 2025.6.27及以后的数据查询 - 新接口
    test('测试新接口查询 (2025.6.27及以后)', () async {
      print('\n=== 测试1: 新接口查询 (2025.6.27及以后) ===');
      
      final startTime = DateTime(2025, 6, 28, 10, 0, 0); // 2025-06-28 10:00:00
      final endTime = DateTime(2025, 6, 28, 11, 0, 0);   // 2025-06-28 11:00:00
      
      print('测试时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
      print('预期策略: 新接口 (2025.6.27及以后)');
      print('预期接口: n9elol.staff.xdf.cn');
      
      try {
        final result = await kibanaService.fetchLogs(
          indexPattern: 'p1-pro-work-wechat-magic',
          startTime: startTime,
          endTime: endTime,
        );
        
        print('查询结果:');
        print('- 总数据量: ${result['logs']?.length ?? 0}');
        print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
        print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
        
        // 验证结果结构
        expect(result, isA<Map<String, dynamic>>());
        expect(result.containsKey('logs'), isTrue);
        expect(result.containsKey('errorLogs'), isTrue);
        
      } catch (e) {
        print('查询失败: $e');
        fail('新接口查询失败: $e');
      }
    });

    /// 测试2: 2025.6.25-26的数据查询 - p1-weixinshengtai大索引
    test('测试p1-weixinshengtai大索引查询 (2025.6.25-26)', () async {
      print('\n=== 测试2: p1-weixinshengtai大索引查询 (2025.6.25-26) ===');
      
      final startTime = DateTime(2025, 6, 25, 10, 0, 0); // 2025-06-25 10:00:00
      final endTime = DateTime(2025, 6, 25, 11, 0, 0);   // 2025-06-25 11:00:00
      
      print('测试时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
      print('预期策略: p1-weixinshengtai大索引 (2025.6.25-26)');
      print('预期接口: kibanalb.staff.xdf.cn');
      print('预期索引: p1-weixinshengtai');
      
      try {
        final result = await kibanaService.fetchLogs(
          indexPattern: 'p1-pro-work-wechat-magic', // 用户选择的索引，但会被覆盖为p1-weixinshengtai
          startTime: startTime,
          endTime: endTime,
        );
        
        print('查询结果:');
        print('- 总数据量: ${result['logs']?.length ?? 0}');
        print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
        print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
        
        // 验证结果结构
        expect(result, isA<Map<String, dynamic>>());
        expect(result.containsKey('logs'), isTrue);
        expect(result.containsKey('errorLogs'), isTrue);
        
      } catch (e) {
        print('查询失败: $e');
        fail('p1-weixinshengtai大索引查询失败: $e');
      }
    });

    /// 测试3: 2025.6.25之前的数据查询 - 原始Kibana查询
    test('测试原始Kibana查询 (2025.6.25之前)', () async {
      print('\n=== 测试3: 原始Kibana查询 (2025.6.25之前) ===');
      
      final startTime = DateTime(2025, 6, 24, 10, 0, 0); // 2025-06-24 10:00:00
      final endTime = DateTime(2025, 6, 24, 11, 0, 0);   // 2025-06-24 11:00:00
      
      print('测试时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
      print('预期策略: 原始Kibana查询 (2025.6.25之前)');
      print('预期接口: kibanalb.staff.xdf.cn');
      print('预期索引: p1-pro-work-wechat-magic (用户选择的索引)');
      
      try {
        final result = await kibanaService.fetchLogs(
          indexPattern: 'p1-pro-work-wechat-magic',
          startTime: startTime,
          endTime: endTime,
        );
        
        print('查询结果:');
        print('- 总数据量: ${result['logs']?.length ?? 0}');
        print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
        print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
        
        // 验证结果结构
        expect(result, isA<Map<String, dynamic>>());
        expect(result.containsKey('logs'), isTrue);
        expect(result.containsKey('errorLogs'), isTrue);
        
      } catch (e) {
        print('查询失败: $e');
        fail('原始Kibana查询失败: $e');
      }
    });

    /// 测试4: 跨时间段查询 - 应该选择最早时间的策略
    test('测试跨时间段查询', () async {
      print('\n=== 测试4: 跨时间段查询 ===');
      
      final startTime = DateTime(2025, 6, 24, 10, 0, 0); // 2025-06-24 (6.25之前)
      final endTime = DateTime(2025, 6, 28, 11, 0, 0);   // 2025-06-28 (6.27之后)
      
      print('测试时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
      print('预期策略: 原始Kibana查询 (根据开始时间2025.6.24选择)');
      print('预期接口: kibanalb.staff.xdf.cn');
      
      try {
        final result = await kibanaService.fetchLogs(
          indexPattern: 'p1-pro-work-wechat-magic',
          startTime: startTime,
          endTime: endTime,
        );
        
        print('查询结果:');
        print('- 总数据量: ${result['logs']?.length ?? 0}');
        print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
        print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
        
        // 验证结果结构
        expect(result, isA<Map<String, dynamic>>());
        expect(result.containsKey('logs'), isTrue);
        expect(result.containsKey('errorLogs'), isTrue);
        
      } catch (e) {
        print('查询失败: $e');
        fail('跨时间段查询失败: $e');
      }
    });

    /// 测试5: 边界时间测试 - 6月25日当天
    test('测试边界时间 - 6月25日当天', () async {
      print('\n=== 测试5: 边界时间 - 6月25日当天 ===');
      
      final startTime = DateTime(2025, 6, 25, 0, 0, 0);  // 2025-06-25 00:00:00
      final endTime = DateTime(2025, 6, 25, 23, 59, 59); // 2025-06-25 23:59:59
      
      print('测试时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
      print('预期策略: p1-weixinshengtai大索引 (2025.6.25-26)');
      print('预期接口: kibanalb.staff.xdf.cn');
      
      try {
        final result = await kibanaService.fetchLogs(
          indexPattern: 'p1-pro-work-wechat-magic',
          startTime: startTime,
          endTime: endTime,
        );
        
        print('查询结果:');
        print('- 总数据量: ${result['logs']?.length ?? 0}');
        print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
        print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
        
        // 验证结果结构
        expect(result, isA<Map<String, dynamic>>());
        expect(result.containsKey('logs'), isTrue);
        expect(result.containsKey('errorLogs'), isTrue);
        
      } catch (e) {
        print('查询失败: $e');
        fail('边界时间查询失败: $e');
      }
    });

    /// 测试6: 边界时间测试 - 6月27日当天
    test('测试边界时间 - 6月27日当天', () async {
      print('\n=== 测试6: 边界时间 - 6月27日当天 ===');
      
      final startTime = DateTime(2025, 6, 27, 0, 0, 0);  // 2025-06-27 00:00:00
      final endTime = DateTime(2025, 6, 27, 23, 59, 59); // 2025-06-27 23:59:59
      
      print('测试时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
      print('预期策略: 新接口 (2025.6.27及以后)');
      print('预期接口: n9elol.staff.xdf.cn');
      
      try {
        final result = await kibanaService.fetchLogs(
          indexPattern: 'p1-pro-work-wechat-magic',
          startTime: startTime,
          endTime: endTime,
        );
        
        print('查询结果:');
        print('- 总数据量: ${result['logs']?.length ?? 0}');
        print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
        print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
        
        // 验证结果结构
        expect(result, isA<Map<String, dynamic>>());
        expect(result.containsKey('logs'), isTrue);
        expect(result.containsKey('errorLogs'), isTrue);
        
      } catch (e) {
        print('查询失败: $e');
        fail('边界时间查询失败: $e');
      }
    });
  });
}
