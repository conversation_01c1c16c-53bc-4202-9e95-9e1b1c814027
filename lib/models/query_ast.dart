// Base class for all nodes in the Abstract Syntax Tree.
abstract class QueryNode {}

// Represents a field-level filter, e.g., "status:200" or "level:error".
class FilterNode extends QueryNode {
  final String key;
  final String operator; // e.g., ":", ":>", ":<", ":>=", ":<="
  final String value;

  FilterNode({required this.key, required this.operator, required this.value});

  @override
  String toString() => 'Filter($key$operator$value)';
}

// Represents a free-text search term, e.g., "login failed"
class FreeTextNode extends QueryNode {
  final String text;

  FreeTextNode(this.text);

  @override
  String toString() => 'Text($text)';
}

// Represents a logical combination of two nodes.
abstract class LogicalNode extends QueryNode {
  final QueryNode left;
  final QueryNode right;

  LogicalNode(this.left, this.right);
}

// Represents a logical AND operation.
class AndNode extends LogicalNode {
  AndNode(QueryNode left, QueryNode right) : super(left, right);

  @override
  String toString() => '($left AND $right)';
}

// Represents a logical OR operation.
class OrNode extends LogicalNode {
  OrNode(QueryNode left, QueryNode right) : super(left, right);

  @override
  String toString() => '($left OR $right)';
}

// Represents a logical NOT operation.
class NotNode extends QueryNode {
  final QueryNode child;

  NotNode(this.child);

  @override
  String toString() => 'NOT ($child)';
} 