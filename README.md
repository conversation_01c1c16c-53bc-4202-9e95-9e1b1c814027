# TPA Log Reader

企业微信自动化脚本日志分析器，支持本地压缩包分析和在线Kibana日志查询。

## 功能特性

### 本地日志压缩包分析器
- ✅ ZIP压缩包自动解压和解析
- ✅ 按日期和会话组织日志数据
- ✅ 支持多种日志类型分类（设备信息、步骤、指令、详细日志）
- ✅ 智能错误检测和标识
- ✅ 图片文件自动识别和展示
- ✅ 历史记录管理
- ✅ 日志内容详细展示和JSON格式化

### 在线Kibana日志查询
- ✅ 连接Kibana服务获取实时日志
- ✅ 支持时间范围查询
- ✅ 支持关键词搜索和过滤

## 项目结构

```
lib/
├── features/
│   └── local_log/           # 本地日志分析功能
│       ├── controllers/     # 控制器
│       ├── models/         # 数据模型
│       ├── services/       # 业务服务
│       └── views/          # 界面视图
├── models/                 # 全局数据模型
├── pages/                  # 页面
├── utils/                  # 工具类
├── widgets/               # 通用组件
├── kibana_service.dart    # Kibana服务
└── main.dart             # 应用入口
```

## 快速开始

### 环境要求
- Flutter SDK >= 3.4.1
- Dart SDK >= 3.4.1
- macOS / iOS / Android 支持

### 安装依赖
```bash
flutter pub get
```

### 运行应用

#### iOS 手机端
```bash
# 启动iOS模拟器
xcrun simctl boot 3D3C4E2B-398B-49E5-ACEC-16392F846D11
# 运行应用
flutter run -d 3D3C4E2B-398B-49E5-ACEC-16392F846D11
```

#### macOS 桌面端
```bash
flutter run -d macos
```

#### Android 端
```bash
flutter run -d 10AF2E18310026T
```

### 测试
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
dart test test/log_error_detection_test.dart
```

## 已知问题和改进计划

### 高优先级问题（需要立即解决）
- [ ] **改善错误信息提示**：技术性错误信息对普通用户不够友好，需要提供更清晰的错误描述和解决建议
- [ ] **添加进度指示器**：大文件处理时缺乏进度反馈，用户体验不佳
- [ ] **实现基本搜索功能**：本地日志分析器缺乏全文搜索能力
- [ ] **完善加载状态指示**：操作反馈不及时，用户不知道当前处理状态

### 中优先级问题（短期改进）
- [ ] **实现分页加载**：大型日志文件一次性加载导致内存压力和UI阻塞
- [ ] **添加数据导出功能**：无法导出过滤结果和生成分析报告
- [ ] **改进临时文件管理**：临时文件清理不完善，可能导致磁盘空间浪费
- [ ] **增加配置选项**：缺乏用户自定义显示偏好和配置管理
- [ ] **时间范围过滤**：本地日志分析器缺乏按时间段过滤功能
- [ ] **高级过滤选项**：只能按类型过滤，缺乏组合条件过滤

### 低优先级问题（长期改进）
- [ ] **重构代码架构**：业务逻辑与UI耦合，错误判断逻辑分散，需要改善代码结构
- [ ] **添加高级分析功能**：缺乏趋势分析、性能指标分析等深度分析功能
- [ ] **支持多种文件格式**：目前只支持ZIP格式和特定JSON格式，扩展性有限
- [ ] **实现插件化架构**：支持自定义日志解析器和分析器
- [ ] **完善测试覆盖率**：缺乏全面的单元测试和集成测试
- [ ] **性能优化**：使用Isolate进行后台解析，实现虚拟滚动列表

### 功能增强建议
- [ ] **统计分析面板**：添加错误统计、执行时间分析、成功率统计等
- [ ] **日志对比功能**：支持多个会话之间的日志对比
- [ ] **自动化报告生成**：定期生成日志分析报告
- [ ] **实时监控模式**：支持实时监控正在执行的脚本
- [ ] **多语言支持**：支持国际化
- [ ] **主题定制**：支持深色模式和主题切换

## 技术栈

- **框架**: Flutter 3.x
- **语言**: Dart 3.x
- **状态管理**: StatefulWidget (计划迁移到Riverpod/Bloc)
- **网络请求**: Dio
- **本地存储**: SharedPreferences
- **文件处理**: archive, file_picker, path_provider
- **UI组件**: Material Design 3
- **测试**: flutter_test, test

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 更新日志

### v1.0.0 (当前版本)
- ✅ 实现本地ZIP日志压缩包分析
- ✅ 实现在线Kibana日志查询
- ✅ 修复步骤100（脚本完成退出）被错误标记为错误的问题
- ✅ 添加错误检测逻辑测试用例
- ✅ 完善日志类型映射和错误码说明