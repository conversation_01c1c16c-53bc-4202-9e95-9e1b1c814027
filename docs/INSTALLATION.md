# TPA Log Reader - 安装指南

## 📱 Android 安装

### 方法一：直接安装 APK
1. 下载 `app-release.apk` 文件到您的 Android 设备
2. 在设备上启用"未知来源"安装：
   - 设置 → 安全 → 未知来源 (Android 7.0 及以下)
   - 设置 → 应用和通知 → 特殊应用访问 → 安装未知应用 (Android 8.0+)
3. 点击 APK 文件进行安装
4. 按照提示完成安装

### 方法二：通过 ADB 安装 (开发者)
```bash
# 连接设备并启用 USB 调试
adb install app-release.apk
```

### 系统要求
- **最低版本**: Android 5.0 (API Level 21)
- **推荐版本**: Android 8.0 或更高
- **存储空间**: 至少 50MB 可用空间

## 🖥️ macOS 安装

### 方法一：使用 DMG 安装包 (推荐)
1. 双击 `TPA-Log-Reader-v1.1.0.dmg` 文件
2. 等待 DMG 挂载完成
3. 将 `tpa_log_reader.app` 拖拽到 `Applications` 文件夹
4. 弹出 DMG 镜像
5. 在启动台或应用程序文件夹中找到并启动应用

### 方法二：直接使用 .app 文件
1. 双击 `tpa_log_reader.app` 直接运行
2. 或将其拖拽到应用程序文件夹中

### 首次运行注意事项

由于应用未经过 Apple 公证，首次运行时可能会遇到安全提示：

#### 解决"无法打开，因为无法验证开发者"错误：
1. **方法一 (推荐)**：
   - 右键点击应用 → 选择"打开"
   - 在弹出的对话框中点击"打开"

2. **方法二**：
   - 系统偏好设置 → 安全性与隐私 → 通用
   - 点击"仍要打开"按钮

3. **方法三 (终端)**：
   ```bash
   sudo xattr -rd com.apple.quarantine /Applications/tpa_log_reader.app
   ```

### 系统要求
- **最低版本**: macOS 10.14 Mojave
- **推荐版本**: macOS 11.0 Big Sur 或更高
- **架构支持**: Intel (x86_64) 和 Apple Silicon (ARM64)
- **存储空间**: 至少 100MB 可用空间

## 🔧 首次使用配置

### Kibana 连接配置
1. 启动应用后，进入 Kibana 日志查询页面
2. 配置 Kibana 服务器地址
3. 设置认证信息（如需要）
4. 测试连接是否正常

### 本地日志分析
1. 点击右上角的压缩包图标
2. 选择要分析的 ZIP 格式日志文件
3. 等待解析完成
4. 开始分析日志数据

## ❓ 常见问题

### Android 相关

**Q: 安装时提示"解析包时出现问题"**
A: 
- 确保下载的 APK 文件完整，没有损坏
- 检查设备存储空间是否充足
- 尝试重新下载 APK 文件

**Q: 应用无法启动或闪退**
A:
- 确保设备 Android 版本不低于 5.0
- 清除应用数据后重试
- 重启设备后再次尝试

### macOS 相关

**Q: 提示"应用已损坏，无法打开"**
A:
- 重新下载 DMG 文件
- 使用终端命令移除隔离属性：
  ```bash
  sudo xattr -rd com.apple.quarantine /Applications/tpa_log_reader.app
  ```

**Q: 应用无法连接到 Kibana**
A:
- 检查网络连接
- 确认 Kibana 服务器地址正确
- 检查防火墙设置
- 确认 Kibana 服务正常运行

**Q: 本地日志文件无法解析**
A:
- 确保日志文件是 ZIP 格式
- 检查 ZIP 文件是否损坏
- 确认日志文件结构符合预期格式

## 🔄 更新应用

### Android
- 下载新版本 APK
- 直接安装覆盖旧版本
- 应用数据会自动保留

### macOS
- 下载新版本 DMG
- 替换应用程序文件夹中的旧版本
- 或直接覆盖安装

## 🗑️ 卸载应用

### Android
- 设置 → 应用管理 → TPA Log Reader → 卸载
- 或长按应用图标 → 卸载

### macOS
- 将应用从应用程序文件夹拖拽到废纸篓
- 清空废纸篓完成卸载

## 📞 技术支持

如果在安装或使用过程中遇到问题：

1. 查看本文档的常见问题部分
2. 确认系统要求是否满足
3. 尝试重新下载和安装
4. 在项目中创建 Issue 并提供详细的错误信息

---

*安装有问题？请参考 [构建指南](BUILD_GUIDE.md) 或联系技术支持*
