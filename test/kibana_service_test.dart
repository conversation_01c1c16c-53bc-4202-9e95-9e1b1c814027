import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import '../lib/kibana_service.dart';
import '../lib/models/query_ast.dart';

// 生成Mock类
@GenerateMocks([Dio])
import 'kibana_service_test.mocks.dart';

void main() {
  group('KibanaService Tests', () {
    late KibanaService kibanaService;
    late MockDio mockDio;

    setUp(() {
      mockDio = MockDio();
      kibanaService = KibanaService();
      // 注入mock dio - 需要修改KibanaService来支持依赖注入
    });

    group('搜索功能测试', () {
      test('应该能搜索email字段', () async {
        // 模拟返回数据
        final mockResponse = Response<Map<String, dynamic>>(
          data: {
            'responses': [
              {
                'hits': {
                  'hits': [
                    {
                      '_source': {
                        'message': 'report task log, email=<EMAIL>, data={"data":{"taskId":"123"}}'
                      }
                    }
                  ],
                  'total': 1
                }
              }
            ]
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockDio.post(any, data: any, options: any))
            .thenAnswer((_) async => mockResponse);

        // 创建email搜索查询
        final emailQuery = FilterNode(key: 'email', operator: ':', value: '<EMAIL>');
        
        final result = await kibanaService.fetchLogs(
          queryAst: emailQuery,
          startTime: DateTime.now().subtract(Duration(hours: 1)),
          endTime: DateTime.now(),
          indexPattern: 'p1-pro-work-wechat-magic',
        );

        expect(result['logs'], isNotEmpty);
        expect(result['total'], equals(1));
      });

      test('应该能搜索appVersion字段', () async {
        final mockResponse = Response<Map<String, dynamic>>(
          data: {
            'responses': [
              {
                'hits': {
                  'hits': [
                    {
                      '_source': {
                        'message': 'report task log, email=<EMAIL>, data={"data":{"device":{"appVersion":"1.3.2"}}}'
                      }
                    }
                  ],
                  'total': 1
                }
              }
            ]
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockDio.post(any, data: any, options: any))
            .thenAnswer((_) async => mockResponse);

        final appVersionQuery = FilterNode(key: 'appVersion', operator: ':', value: '1.3.2');
        
        final result = await kibanaService.fetchLogs(
          queryAst: appVersionQuery,
          startTime: DateTime.now().subtract(Duration(hours: 1)),
          endTime: DateTime.now(),
          indexPattern: 'p1-pro-work-wechat-magic',
        );

        expect(result['logs'], isNotEmpty);
        expect(result['total'], equals(1));
      });

      test('应该能搜索platformName字段', () async {
        final mockResponse = Response<Map<String, dynamic>>(
          data: {
            'responses': [
              {
                'hits': {
                  'hits': [
                    {
                      '_source': {
                        'message': 'report task log, email=<EMAIL>, data={"data":{"device":{"platformName":"macos"}}}'
                      }
                    }
                  ],
                  'total': 1
                }
              }
            ]
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockDio.post(any, data: any, options: any))
            .thenAnswer((_) async => mockResponse);

        final platformQuery = FilterNode(key: 'platformName', operator: ':', value: 'macos');
        
        final result = await kibanaService.fetchLogs(
          queryAst: platformQuery,
          startTime: DateTime.now().subtract(Duration(hours: 1)),
          endTime: DateTime.now(),
          indexPattern: 'p1-pro-work-wechat-magic',
        );

        expect(result['logs'], isNotEmpty);
        expect(result['total'], equals(1));
      });
    });

    group('时间范围测试', () {
      test('应该能根据时间范围过滤日志', () async {
        final mockResponse = Response<Map<String, dynamic>>(
          data: {
            'responses': [
              {
                'hits': {
                  'hits': [
                    {
                      '_source': {
                        '@timestamp': 1751212639735,
                        'message': 'report task log, email=<EMAIL>, data={"data":{"taskId":"123"}}'
                      }
                    }
                  ],
                  'total': 1
                }
              }
            ]
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: ''),
        );

        when(mockDio.post(any, data: any, options: any))
            .thenAnswer((_) async => mockResponse);

        final startTime = DateTime.fromMillisecondsSinceEpoch(1751212600000);
        final endTime = DateTime.fromMillisecondsSinceEpoch(1751212700000);
        
        final result = await kibanaService.fetchLogs(
          startTime: startTime,
          endTime: endTime,
          indexPattern: 'p1-pro-work-wechat-magic',
        );

        expect(result['logs'], isNotEmpty);
        expect(result['total'], equals(1));
      });
    });
  });

  group('查询构建测试', () {
    late KibanaService kibanaService;

    setUp(() {
      kibanaService = KibanaService();
    });

    test('应该为email字段生成正确的查询', () {
      final emailQuery = FilterNode(key: 'email', operator: ':', value: '<EMAIL>');
      final result = kibanaService.buildQueryFromAstForTesting(emailQuery);
      
      expect(result['match_phrase'], isNotNull);
      expect(result['match_phrase']['message']['query'], equals('email=<EMAIL>'));
    });

    test('应该为appVersion字段生成正确的查询', () {
      final appVersionQuery = FilterNode(key: 'appVersion', operator: ':', value: '1.3.2');
      final result = kibanaService.buildQueryFromAstForTesting(appVersionQuery);
      
      expect(result['match_phrase'], isNotNull);
      expect(result['match_phrase']['message']['query'], equals('"appVersion":"1.3.2"'));
    });

    test('应该为platformName字段生成正确的查询', () {
      final platformQuery = FilterNode(key: 'platformName', operator: ':', value: 'macos');
      final result = kibanaService.buildQueryFromAstForTesting(platformQuery);
      
      expect(result['match_phrase'], isNotNull);
      expect(result['match_phrase']['message']['query'], equals('"platformName":"macos"'));
    });
  });
}
