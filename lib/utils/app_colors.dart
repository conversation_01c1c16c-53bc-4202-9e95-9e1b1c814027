 import 'package:flutter/material.dart';

/// 根据HTTP状态码返回对应的颜色。
Color getStatusColor(String status) {
  if (status.startsWith('5')) return Colors.red.shade600;
  if (status.startsWith('4')) return Colors.orange.shade600;
  if (status.startsWith('3')) return Colors.blue.shade600;
  if (status.startsWith('2')) return Colors.green.shade600;
  return Colors.grey.shade500;
}

/// 根据响应时间返回对应的颜色。
Color getResponseTimeColor(String responseTime) {
  if (responseTime.isEmpty) return Colors.grey.shade500;

  final time = double.tryParse(responseTime) ?? 0;
  if (time > 2.0) return Colors.red.shade700;
  if (time > 1.0) return Colors.orange.shade700;
  if (time > 0.5) return Colors.yellow.shade800;
  return Colors.green.shade700;
}