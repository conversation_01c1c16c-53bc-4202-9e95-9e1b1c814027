# TPA Log Reader - 打包构建指南

## 📋 概述

本文档详细介绍了如何使用自动化构建脚本为 TPA Log Reader 项目构建生产版本的应用程序包，包括 Android APK 和 macOS DMG 安装包。

## 🛠️ 环境要求

### 基础环境
- **Flutter SDK**: >= 3.4.1
- **Dart SDK**: >= 3.4.1
- **操作系统**: macOS (推荐，支持全平台构建)

### Android 构建要求
- **Android SDK**: 通过 Flutter 安装
- **Java**: OpenJDK 8 或更高版本

### macOS 构建要求
- **Xcode**: 最新版本 (仅 macOS 系统)
- **Homebrew**: 用于安装 create-dmg 工具
- **create-dmg**: 自动安装 (如未安装)

## 🚀 快速开始

### 一键构建 (推荐)

使用提供的自动化构建脚本，一次性构建所有平台：

```bash
# 进入项目根目录
cd tpa_log_reader

# 运行自动化构建脚本
./scripts/build_all.sh
```

### 手动构建

如果需要单独构建某个平台：

#### Android APK
```bash
flutter clean
flutter pub get
flutter build apk --release
```

#### macOS 应用
```bash
flutter clean
flutter pub get
flutter build macos --release
```

#### macOS DMG (需要先构建 macOS 应用)
```bash
# 安装 create-dmg (如未安装)
brew install create-dmg

# 创建 DMG
create-dmg \
    --volname "TPA Log Reader" \
    --window-pos 200 120 \
    --window-size 800 400 \
    --icon-size 100 \
    --icon "tpa_log_reader.app" 200 190 \
    --hide-extension "tpa_log_reader.app" \
    --app-drop-link 600 190 \
    "build/TPA-Log-Reader-v1.1.0.dmg" \
    "build/macos/Build/Products/Release/"
```

## 📦 构建输出

### 文件位置和大小

构建完成后，您将获得以下文件：

| 平台 | 文件路径 | 文件大小 | 用途 |
|------|----------|----------|------|
| Android | `build/app/outputs/flutter-apk/app-release.apk` | ~24MB | Android 设备安装包 |
| macOS | `build/macos/Build/Products/Release/tpa_log_reader.app` | ~55MB | macOS 应用程序 |
| macOS | `build/TPA-Log-Reader-v1.1.0.dmg` | ~177MB | macOS 安装镜像 |

### 构建脚本输出示例

```
🎉 构建完成!
================================
📱 Android APK: build/app/outputs/flutter-apk/app-release.apk
🖥️  macOS App: build/macos/Build/Products/Release/tpa_log_reader.app
📀 macOS DMG: build/TPA-Log-Reader-v1.1.0.dmg

💡 提示:
- Android APK 可直接安装到 Android 设备
- macOS DMG 可直接分发给用户，双击挂载后拖拽安装
- macOS App 可直接运行或拖拽到应用程序文件夹
- 如需正式分发，请考虑代码签名和公证
```

## 🔧 构建脚本功能

### 自动化功能
- ✅ **环境检查**: 自动检测 Flutter 环境是否正确配置
- ✅ **依赖管理**: 自动清理和重新安装项目依赖
- ✅ **多平台构建**: 同时构建 Android 和 macOS 版本
- ✅ **DMG 创建**: 自动创建 macOS 安装镜像
- ✅ **工具安装**: 自动安装 create-dmg (通过 Homebrew)
- ✅ **构建验证**: 显示构建结果和文件信息

### 错误处理
- 构建失败时自动停止并显示错误信息
- 缺少依赖工具时自动尝试安装
- 提供详细的错误诊断信息

## 📱 分发指南

### Android APK 分发

**内部测试**:
```bash
# 通过 ADB 安装到连接的设备
adb install build/app/outputs/flutter-apk/app-release.apk
```

**文件分享**:
- 直接分享 APK 文件给用户
- 用户需要在设备上启用"未知来源"安装

**应用商店发布**:
- 需要进行代码签名
- 建议构建 App Bundle: `flutter build appbundle --release`

### macOS 应用分发

**个人使用**:
- 直接运行 `.app` 文件
- 或拖拽到应用程序文件夹

**团队内部分发** (推荐):
- 分享 `.dmg` 文件
- 用户双击挂载，拖拽安装

**公开发布**:
- 需要 Apple Developer 账号
- 进行代码签名和公证
- 提交到 Mac App Store 或独立分发

## ⚠️ 注意事项

### 代码签名

**macOS 应用**:
```bash
# 签名应用 (需要开发者证书)
codesign --force --deep --sign "Developer ID Application: Your Name" \
    build/macos/Build/Products/Release/tpa_log_reader.app

# 公证应用 (可选，用于公开分发)
xcrun notarytool submit build/TPA-Log-Reader-v1.1.0.dmg \
    --apple-id <EMAIL> \
    --password your-app-password \
    --team-id YOUR_TEAM_ID
```

**Android 应用**:
```bash
# 生成签名密钥 (首次)
keytool -genkey -v -keystore ~/upload-keystore.jks \
    -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# 配置签名 (在 android/app/build.gradle 中)
```

### 系统兼容性

| 平台 | 最低版本 | 推荐版本 |
|------|----------|----------|
| Android | 5.0 (API 21) | 8.0+ |
| macOS | 10.14 Mojave | 11.0+ |

### 文件大小优化

如需减小应用大小，可以考虑：

```bash
# Android - 构建分架构 APK
flutter build apk --release --split-per-abi

# 启用代码混淆
flutter build apk --release --obfuscate --split-debug-info=build/debug-info/
```

## 🐛 常见问题

### 构建失败

**Flutter 环境问题**:
```bash
flutter doctor -v  # 检查环境
flutter clean      # 清理项目
flutter pub get    # 重新获取依赖
```

**Android 构建失败**:
- 检查 Java 版本: `java -version`
- 更新 Android SDK: `flutter doctor --android-licenses`

**macOS 构建失败**:
- 更新 Xcode: App Store
- 清理 Xcode 缓存: `rm -rf ~/Library/Developer/Xcode/DerivedData`

### DMG 创建失败

**create-dmg 未安装**:
```bash
# 手动安装
brew install create-dmg

# 或使用 MacPorts
sudo port install create-dmg
```

**权限问题**:
```bash
# 给脚本执行权限
chmod +x scripts/build_all.sh
```

## 📞 技术支持

如遇到构建问题，请：

1. 检查环境配置: `flutter doctor -v`
2. 查看构建日志中的错误信息
3. 参考本文档的常见问题部分
4. 在项目中创建 Issue 并附上详细的错误信息

---

*最后更新: 2025-07-02*
