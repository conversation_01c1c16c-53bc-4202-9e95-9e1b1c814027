import 'package:flutter/foundation.dart';

@immutable
class LogSession {
  final String path;
  final String date;
  final String time;
  final String name;
  final List<String> imagePaths;

  const LogSession({
    required this.path,
    required this.date,
    required this.time,
    required this.name,
    this.imagePaths = const [],
  });

  String get displayName => '会话: ${time.replaceAll('_', ':')}';
  String get fullPath => path;
}
