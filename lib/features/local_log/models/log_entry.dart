import 'package:flutter/foundation.dart';

@immutable
abstract class LogEntry {
  final int type;
  final int date;

  const LogEntry({required this.type, required this.date});

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 0:
        return InfoEntry.fromJson(json);
      case 1:
        return StepEntry.fromJson(json);
      case 2:
        return CmdEntry.fromJson(json);
      case 3:
        return DetailEntry.fromJson(json);
      default:
        // 对于未知类型，可以返回一个默认实现或抛出异常
        return UnknownEntry.fromJson(json);
    }
  }
}

// Type 0: App Info
class InfoEntry extends LogEntry {
  final Map<String, dynamic> info;
  const InfoEntry({required super.date, required this.info}) : super(type: 0);

  factory InfoEntry.fromJson(Map<String, dynamic> json) {
    return InfoEntry(date: json['date'] ?? 0, info: json['info'] ?? {});
  }
}

// Type 1: Step
class StepEntry extends LogEntry {
  final int step;
  final Map<String, dynamic>? args;
  const StepEntry({required super.date, required this.step, this.args}) : super(type: 1);

  factory StepEntry.fromJson(Map<String, dynamic> json) {
    return StepEntry(date: json['date'], step: json['step'], args: json['args']);
  }
}

// Type 2: Command
class CmdEntry extends LogEntry {
  final int cmd;
  final Map<String, dynamic>? args;
  const CmdEntry({required super.date, required this.cmd, this.args}) : super(type: 2);

  factory CmdEntry.fromJson(Map<String, dynamic> json) {
    return CmdEntry(date: json['date'], cmd: json['cmd'], args: json['args']);
  }
}

// Type 3: Detail Info
class DetailEntry extends LogEntry {
  final String info;
  final Map<String, dynamic> args;
  const DetailEntry({required super.date, required this.info, required this.args}) : super(type: 3);

  factory DetailEntry.fromJson(Map<String, dynamic> json) {
    return DetailEntry(date: json['date'], info: json['info'], args: json['args']);
  }

  // Helper to check if this entry is an unmatch log with an image path
  bool get isUnmatchImage {
    return info == 'unmatch' && args.containsKey('unmatch_path');
  }

  String? get imagePath => isUnmatchImage ? args['unmatch_path'] : null;
}

// Fallback for unknown types
class UnknownEntry extends LogEntry {
  final Map<String, dynamic> data;
  const UnknownEntry({required super.date, required this.data}) : super(type: -1);

   factory UnknownEntry.fromJson(Map<String, dynamic> json) {
    return UnknownEntry(date: json['date'] ?? 0, data: json);
  }
}
