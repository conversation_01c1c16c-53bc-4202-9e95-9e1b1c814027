import 'package:flutter_test/flutter_test.dart';
import '../lib/utils/query_parser.dart';
import '../lib/models/query_ast.dart';
import '../lib/kibana_service.dart';

void main() {
  group('综合搜索功能测试', () {
    late KibanaService kibanaService;

    setUp(() {
      kibanaService = KibanaService();
    });

    test('应该正确解析和构建email字段查询', () {
      const queryString = 'email: <EMAIL>';
      final parsedQuery = QueryParser.parse(queryString);
      
      expect(parsedQuery.astRoot, isA<FilterNode>());
      final filterNode = parsedQuery.astRoot as FilterNode;
      expect(filterNode.key, equals('email'));
      expect(filterNode.value, equals('<EMAIL>'));
      expect(filterNode.operator, equals(':'));
      
      // 测试查询构建
      final queryBody = kibanaService.buildQueryFromAstForTesting(filterNode);
      expect(queryBody['query']['bool']['must'], hasLength(2)); // report task log + email filter
      
      final emailFilter = queryBody['query']['bool']['must'][1];
      expect(emailFilter['match_phrase']['message']['query'], equals('email=<EMAIL>'));
      
      print('✓ Email字段查询测试通过');
    });

    test('应该正确解析和构建platformName字段查询', () {
      const queryString = 'platformName: windows';
      final parsedQuery = QueryParser.parse(queryString);
      
      expect(parsedQuery.astRoot, isA<FilterNode>());
      final filterNode = parsedQuery.astRoot as FilterNode;
      expect(filterNode.key, equals('platformName'));
      expect(filterNode.value, equals('windows'));
      
      // 测试查询构建
      final queryBody = kibanaService.buildQueryFromAstForTesting(filterNode);
      final platformFilter = queryBody['query']['bool']['must'][1];
      expect(platformFilter['match_phrase']['message']['query'], equals('"platformName":"windows"'));
      
      print('✓ PlatformName字段查询测试通过');
    });

    test('应该正确解析和构建appVersion字段查询', () {
      const queryString = 'appVersion: 1.2.3';
      final parsedQuery = QueryParser.parse(queryString);
      
      expect(parsedQuery.astRoot, isA<FilterNode>());
      final filterNode = parsedQuery.astRoot as FilterNode;
      expect(filterNode.key, equals('appVersion'));
      expect(filterNode.value, equals('1.2.3'));
      
      // 测试查询构建
      final queryBody = kibanaService.buildQueryFromAstForTesting(filterNode);
      final appVersionFilter = queryBody['query']['bool']['must'][1];
      expect(appVersionFilter['match_phrase']['message']['query'], equals('"appVersion":"1.2.3"'));
      
      print('✓ AppVersion字段查询测试通过');
    });

    test('应该正确解析和构建复合查询', () {
      const queryString = 'email: <EMAIL> AND platformName: macos';
      final parsedQuery = QueryParser.parse(queryString);
      
      expect(parsedQuery.astRoot, isA<AndNode>());
      final andNode = parsedQuery.astRoot as AndNode;
      
      expect(andNode.left, isA<FilterNode>());
      expect(andNode.right, isA<FilterNode>());
      
      final leftFilter = andNode.left as FilterNode;
      final rightFilter = andNode.right as FilterNode;
      
      expect(leftFilter.key, equals('email'));
      expect(leftFilter.value, equals('<EMAIL>'));
      expect(rightFilter.key, equals('platformName'));
      expect(rightFilter.value, equals('macos'));
      
      // 测试查询构建
      final queryBody = kibanaService.buildQueryFromAstForTesting(andNode);
      expect(queryBody['query']['bool']['must'], hasLength(2)); // report task log + AND node
      
      final andFilter = queryBody['query']['bool']['must'][1];
      expect(andFilter['bool']['must'], hasLength(2)); // email + platformName
      
      print('✓ 复合查询测试通过');
    });

    test('应该将非字段查询解析为自由文本', () {
      const queryString = 'some random text';
      final parsedQuery = QueryParser.parse(queryString);
      
      expect(parsedQuery.astRoot, isA<FreeTextNode>());
      final textNode = parsedQuery.astRoot as FreeTextNode;
      expect(textNode.text, equals('some random text'));
      
      // 测试查询构建
      final queryBody = kibanaService.buildQueryFromAstForTesting(textNode);
      final textFilter = queryBody['query']['bool']['must'][1];
      expect(textFilter['match_phrase']['message']['query'], equals('some random text'));
      expect(textFilter['match_phrase']['message']['slop'], equals(1));
      
      print('✓ 自由文本查询测试通过');
    });

    test('应该正确处理所有特殊message字段', () {
      final specialFields = [
        'email',
        'taskId', 
        'appVersion',
        'pythonVersion',
        'scriptVersion',
        'platformName',
        'systemArchs',
        'systemVersion',
        'systemName',
        'systemBitness'
      ];
      
      for (final field in specialFields) {
        final queryString = '$field: testValue';
        final parsedQuery = QueryParser.parse(queryString);
        
        expect(parsedQuery.astRoot, isA<FilterNode>());
        final filterNode = parsedQuery.astRoot as FilterNode;
        expect(filterNode.key, equals(field));
        expect(filterNode.value, equals('testValue'));
        
        // 测试查询构建
        final queryBody = kibanaService.buildQueryFromAstForTesting(filterNode);
        final fieldFilter = queryBody['query']['bool']['must'][1];
        
        if (field == 'email') {
          expect(fieldFilter['match_phrase']['message']['query'], equals('$field=testValue'));
        } else {
          expect(fieldFilter['match_phrase']['message']['query'], equals('"$field":"testValue"'));
        }
      }
      
      print('✓ 所有特殊message字段测试通过');
    });
  });
}
