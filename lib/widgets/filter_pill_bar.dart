import 'package:flutter/material.dart';
import '../models/filter.dart';

class FilterPillBar extends StatelessWidget {
  const FilterPillBar({
    super.key,
    required this.filters,
    required this.onFilterDeleted,
  });

  final List<Filter> filters;
  final ValueChanged<Filter> onFilterDeleted;

  @override
  Widget build(BuildContext context) {
    if (filters.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Wrap(
        spacing: 8.0,
        runSpacing: 4.0,
        children: filters.map((filter) {
          return Chip(
            label: Text('${filter.key}${filter.operator}${filter.value}'),
            onDeleted: () => onFilterDeleted(filter),
            deleteIconColor: Colors.red.shade400,
            backgroundColor: Colors.blue.shade50,
            labelStyle: TextStyle(color: Colors.blue.shade800, fontWeight: FontWeight.w500),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.blue.shade200),
            ),
          );
        }).toList(),
      ),
    );
  }
} 