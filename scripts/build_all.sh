#!/bin/bash

# TPA Log Reader - 全平台构建脚本
# 用于自动构建 Android APK 和 macOS 应用

set -e  # 遇到错误时退出

echo "🚀 开始构建 TPA Log Reader..."
echo "================================"

# 检查 Flutter 环境
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter 未安装或未添加到 PATH"
    echo "请先安装 Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
fi

echo "✅ Flutter 环境检查通过"
flutter --version

# 清理之前的构建
echo ""
echo "🧹 清理之前的构建..."
flutter clean
flutter pub get

# 构建 Android APK
echo ""
echo "📱 开始构建 Android APK..."
echo "--------------------------------"
flutter build apk --release

if [ $? -eq 0 ]; then
    APK_SIZE=$(du -h build/app/outputs/flutter-apk/app-release.apk | cut -f1)
    echo "✅ Android APK 构建成功!"
    echo "📦 文件位置: build/app/outputs/flutter-apk/app-release.apk"
    echo "📏 文件大小: $APK_SIZE"
else
    echo "❌ Android APK 构建失败"
    exit 1
fi

# 构建 macOS 应用 (仅在 macOS 上)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo ""
    echo "🖥️  开始构建 macOS 应用..."
    echo "--------------------------------"
    flutter build macos --release

    if [ $? -eq 0 ]; then
        APP_SIZE=$(du -sh build/macos/Build/Products/Release/tpa_log_reader.app | cut -f1)
        echo "✅ macOS 应用构建成功!"
        echo "📦 文件位置: build/macos/Build/Products/Release/tpa_log_reader.app"
        echo "📏 文件大小: $APP_SIZE"

        # 创建 DMG 安装包
        echo ""
        echo "📀 开始创建 DMG 安装包..."
        echo "--------------------------------"

        # 检查是否安装了 create-dmg
        if ! command -v create-dmg &> /dev/null; then
            echo "⚠️  create-dmg 未安装，尝试使用 Homebrew 安装..."
            if command -v brew &> /dev/null; then
                brew install create-dmg
            else
                echo "❌ Homebrew 未安装，无法自动安装 create-dmg"
                echo "请手动安装: brew install create-dmg"
                echo "或访问: https://github.com/create-dmg/create-dmg"
                exit 1
            fi
        fi

        # 设置 DMG 相关变量
        DMG_NAME="TPA-Log-Reader-v1.1.0"
        DMG_PATH="build/macos/Build/Products/Release/${DMG_NAME}.dmg"
        APP_PATH="build/macos/Build/Products/Release/tpa_log_reader.app"
        TEMP_DMG_DIR="build/dmg_temp"

        # 删除已存在的 DMG 文件和临时目录
        if [ -f "$DMG_PATH" ]; then
            rm "$DMG_PATH"
            echo "🗑️  删除已存在的 DMG 文件"
        fi
        if [ -d "$TEMP_DMG_DIR" ]; then
            rm -rf "$TEMP_DMG_DIR"
        fi

        # 创建临时目录并只复制需要的文件
        mkdir -p "$TEMP_DMG_DIR"
        cp -R "$APP_PATH" "$TEMP_DMG_DIR/"

        # 创建 DMG
        create-dmg \
            --volname "TPA Log Reader" \
            --window-pos 200 120 \
            --window-size 800 400 \
            --icon-size 100 \
            --icon "tpa_log_reader.app" 200 190 \
            --hide-extension "tpa_log_reader.app" \
            --app-drop-link 600 190 \
            "$DMG_PATH" \
            "$TEMP_DMG_DIR"

        # 清理临时目录
        rm -rf "$TEMP_DMG_DIR"

        if [ $? -eq 0 ]; then
            DMG_SIZE=$(du -sh "$DMG_PATH" | cut -f1)
            echo "✅ DMG 安装包创建成功!"
            echo "📦 文件位置: $DMG_PATH"
            echo "📏 文件大小: $DMG_SIZE"
            echo "🧹 临时文件已清理"
        else
            echo "⚠️  DMG 创建失败，但 .app 文件可正常使用"
            # 清理临时目录（即使失败也要清理）
            if [ -d "$TEMP_DMG_DIR" ]; then
                rm -rf "$TEMP_DMG_DIR"
            fi
        fi
    else
        echo "❌ macOS 应用构建失败"
        exit 1
    fi
else
    echo "⚠️  跳过 macOS 构建 (当前系统不是 macOS)"
fi

# 构建总结
echo ""
echo "🎉 构建完成!"
echo "================================"
echo "📱 Android APK: build/app/outputs/flutter-apk/app-release.apk"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🖥️  macOS App: build/macos/Build/Products/Release/tpa_log_reader.app"
    if [ -f "build/macos/Build/Products/Release/TPA-Log-Reader-v1.1.0.dmg" ]; then
        echo "📀 macOS DMG: build/macos/Build/Products/Release/TPA-Log-Reader-v1.1.0.dmg"
    fi
fi
echo ""
echo "💡 提示:"
echo "- Android APK 可直接安装到 Android 设备"
if [[ "$OSTYPE" == "darwin"* ]]; then
    if [ -f "build/macos/Build/Products/Release/TPA-Log-Reader-v1.1.0.dmg" ]; then
        echo "- macOS DMG 可直接分发给用户，双击挂载后拖拽安装"
        echo "- macOS App 可直接运行或拖拽到应用程序文件夹"
    else
        echo "- macOS 应用可直接运行或拖拽到应用程序文件夹"
    fi
fi
echo "- 如需正式分发，请考虑代码签名和公证"
echo ""
echo "🚀 分发建议:"
echo "- 个人使用: 直接使用 .app 文件"
echo "- 团队分发: 推荐使用 .dmg 文件"
echo "- 公开发布: 需要进行代码签名和公证"
