import '../models/filter.dart';
import '../models/query_ast.dart';

// 定义一个自定义异常，用于表示解析错误
class QueryParseException implements Exception {
  final String message;
  QueryParseException(this.message);

  @override
  String toString() => message;
}

// Represents the output of the parser.
// It contains the root of the AST for building Elasticsearch queries,
// and a flat list of filters for displaying UI pills.
class ParsedQuery {
  final QueryNode? astRoot;
  final List<Filter> filters;

  ParsedQuery({this.astRoot, required this.filters});
}

// --- Tokenizer ---

enum TokenType {
  WORD,
  STRING,
  OPERATOR,
  LPAREN,
  RPAREN,
  AND,
  OR,
  NOT,
  EOF
}

class Token {
  final TokenType type;
  final String value;

  Token(this.type, this.value);

  @override
  String toString() => 'Token($type, "$value")';
}

class _Tokenizer {
  final String _text;
  int _pos = 0;

  _Tokenizer(this._text);

  List<Token> tokenize() {
    final tokens = <Token>[];
    while (_pos < _text.length) {
      final char = _text[_pos];
      if (RegExp(r'\s').hasMatch(char)) {
        _pos++;
        continue;
      }
      if (char == '(') {
        tokens.add(Token(TokenType.LPAREN, char));
        _pos++;
        continue;
      }
      if (char == ')') {
        tokens.add(Token(TokenType.RPAREN, char));
        _pos++;
        continue;
      }
      if (char == '"') {
        tokens.add(Token(TokenType.STRING, _string()));
        continue;
      }
      if (char == ':' || char == '>' || char == '<') {
        final op = _operator();
        tokens.add(Token(TokenType.OPERATOR, op));
        continue;
      }
      
      final word = _word();
      if (word.toUpperCase() == 'AND') {
        tokens.add(Token(TokenType.AND, word));
      } else if (word.toUpperCase() == 'OR') {
        tokens.add(Token(TokenType.OR, word));
      } else if (word.toUpperCase() == 'NOT') {
        tokens.add(Token(TokenType.NOT, word));
      } else {
        tokens.add(Token(TokenType.WORD, word));
      }
    }
    tokens.add(Token(TokenType.EOF, ''));
    return tokens;
  }

  String _word() {
    final start = _pos;
    // 增强正则表达式以包含更多在值中可能出现的字符
    // 包括 @, :, / 等，但排除作为分隔符的空格和括号
    while (_pos < _text.length && RegExp(r'[^()"\s]').hasMatch(_text[_pos])) {
      // 如果遇到冒号，需要向后看，确保它不是一个独立的运算符
      if (_text[_pos] == ':') {
        // 如果冒号后面是空格或字符串结尾，那么它可能是一个运算符，在此处断开
        if (_pos + 1 == _text.length || RegExp(r'\s').hasMatch(_text[_pos + 1])) {
          break;
        }
      }
      _pos++;
    }
    return _text.substring(start, _pos);
  }
  
  String _operator() {
    final start = _pos;
     if (_text.startsWith(':', _pos)) _pos++;
     if (_text.startsWith('>=', _pos) || _text.startsWith('<=', _pos)) {
        _pos += 2;
     } else if (_text.startsWith('>', _pos) || _text.startsWith('<', _pos)) {
       _pos++;
     }
     if (_pos > start) return _text.substring(start, _pos);
     
     // Fallback for single char ops if needed, though covered above
     if (RegExp(r'[:<>]').hasMatch(_text[start])) {
       _pos++;
       return _text.substring(start, _pos);
     }
     
     throw Exception('Invalid operator at $start');
  }

  String _string() {
    _pos++; // Skip opening quote
    final start = _pos;
    while (_pos < _text.length && _text[_pos] != '"') {
      _pos++;
    }
    final value = _text.substring(start, _pos);
    if (_pos < _text.length && _text[_pos] == '"') {
      _pos++; // Skip closing quote
    }
    return value;
  }
}

// --- Parser (Recursive Descent) ---

class QueryParser {
  List<Token> _tokens = [];
  int _pos = 0;
  final List<Filter> _extractedFilters = [];
  String _originalQuery = ''; // 新增：保存原始查询字符串

  Token get _current => _tokens[_pos];

  static ParsedQuery parse(String queryString) {
    if (queryString.trim().isEmpty) {
      return ParsedQuery(filters: []);
    }
    return QueryParser()._parse(queryString);
  }

  ParsedQuery _parse(String queryString) {
    try {
      _originalQuery = queryString; // 新增：保存原始查询
      _tokens = _Tokenizer(queryString).tokenize();
      _pos = 0;
      _extractedFilters.clear();
      
      final astRoot = _expression();

      if (_current.type != TokenType.EOF) {
        // 如果在解析完表达式后没有到达末尾，说明有无法识别的语法
        throw QueryParseException('查询语法无效，请检查 "${_current.value}" 附近的内容。');
      }

      return ParsedQuery(astRoot: astRoot, filters: _extractedFilters);
    } on Exception catch (e) {
      if (e is QueryParseException) rethrow;
      throw QueryParseException('无法解析查询：${e.toString()}');
    }
  }

  QueryNode? _expression() {
    return _or();
  }

  QueryNode? _or() {
    var node = _and();
    while (_current.type == TokenType.OR) {
      _pos++;
      final right = _and();
      if (node != null && right != null) {
        node = OrNode(node, right);
      } else {
        node = node ?? right;
      }
    }
    return node;
  }

  QueryNode? _and() {
    var node = _not();
    while (_current.type == TokenType.AND) {
      _pos++;
      final right = _not();
       if (node != null && right != null) {
        node = AndNode(node, right);
      } else {
        node = node ?? right;
      }
    }
    return node;
  }

  QueryNode? _not() {
    if (_current.type == TokenType.NOT) {
      _pos++;
      final node = _primary();
      return node != null ? NotNode(node) : null;
    }
    return _primary();
  }

  QueryNode? _primary() {
    if (_current.type == TokenType.LPAREN) {
      _pos++;
      final node = _expression();
      if (_current.type != TokenType.RPAREN) {
         // This can happen with inputs like `(foo`
         throw QueryParseException('语法错误：期待一个右括号 )');
      }
      _pos++;
      return node;
    }

    // Look ahead for field-level filter pattern: WORD OPERATOR (WORD|STRING)
    if (_current.type == TokenType.WORD && _pos + 1 < _tokens.length) {
      final nextToken = _tokens[_pos + 1];
      if (nextToken.type == TokenType.OPERATOR) {
        return _filter();
      }
    }

    // --- 修改逻辑：智能判断是全文搜索还是字段过滤 ---
    // 如果查询不包含逻辑运算符，但包含字段过滤模式，优先处理为字段过滤
    final hasLogicOperators = RegExp(r'\s(AND|OR|NOT)\s|[\(\)]', caseSensitive: false).hasMatch(_originalQuery);
    final hasFieldPattern = RegExp(r'\w+\s*:\s*\S+').hasMatch(_originalQuery);

    if (!hasLogicOperators && hasFieldPattern && (_current.type == TokenType.WORD || _current.type == TokenType.STRING)) {
      // 这是一个简单的字段过滤查询，尝试解析为FilterNode
      // 但由于tokenizer可能没有正确分割，我们需要手动解析
      final match = RegExp(r'^(\w+)\s*:\s*(.+)$').firstMatch(_originalQuery.trim());
      if (match != null) {
        final key = match.group(1)!;
        final value = match.group(2)!;
        final filter = Filter(key: key, operator: ':', value: value);
        _extractedFilters.add(filter);
        // 一次性消费掉所有 token，因为我们已经处理了整个查询
        _pos = _tokens.length - 1;
        return FilterNode(key: key, operator: ':', value: value);
      }
    }

    if (!hasLogicOperators && (_current.type == TokenType.WORD || _current.type == TokenType.STRING)) {
      // 这是一个简单的全文搜索查询
      final node = FreeTextNode(_originalQuery.trim());
      // 一次性消费掉所有 token，因为我们已经处理了整个查询
      _pos = _tokens.length - 1;
      return node;
    }
    // --- 修改逻辑结束 ---

    if (_current.type == TokenType.WORD || _current.type == TokenType.STRING) {
       final node = FreeTextNode(_current.value);
       _pos++;
       return node;
    }

    return null;
  }

  FilterNode _filter() {
    final key = _current.value;
    _pos++; // consume key
    
    final operator = _current.value;
    _pos++; // consume operator
    
    final value = _current.value;
    _pos++; // consume value

    final filter = Filter(key: key, operator: operator, value: value);
    _extractedFilters.add(filter);
    return FilterNode(key: key, operator: operator, value: value);
  }
} 