import 'package:flutter/material.dart';
import 'package:tpa_log_reader/widgets/message_content_view.dart';

import '../models/log_session.dart';
import '../services/log_parser.dart';

class LogDetailView extends StatefulWidget {
  final LogSession session;
  const LogDetailView({super.key, required this.session});

  @override
  State<LogDetailView> createState() => _LogDetailViewState();
}

class _LogDetailViewState extends State<LogDetailView> {
  final LogParserService _parserService = LogParserService();
  late Future<List<Map<String, dynamic>>> _logEntriesFuture;

  @override
  void initState() {
    super.initState();
    _logEntriesFuture = _parserService.parseLogFile(widget.session.fullPath);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _logEntriesFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text('加载日志失败: ${snapshot.error}'));
        }
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('该日志文件为空或解析失败。'));
        }

        final entries = snapshot.data!;
        return Scaffold(
          backgroundColor: Colors.white,
          body: Scrollbar(
            thumbVisibility: true,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(12.0),
              child: MessageContentView(content: entries),
            ),
          ),
        );
      },
    );
  }
}
