import 'package:flutter/material.dart';

class LogFilterPanel extends StatelessWidget {
  final Set<String> activeLevelFilters;
  final Function(String) onLevelFilterToggled;

  const LogFilterPanel({
    super.key,
    required this.activeLevelFilters,
    required this.onLevelFilterToggled,
  });

  @override
  Widget build(BuildContext context) {
    // For now, hardcoding log levels. We can make this dynamic later.
    final List<String> logLevels = ['error', 'warn', 'info', 'debug'];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Wrap(
        spacing: 8.0,
        runSpacing: 4.0,
        children: logLevels.map((level) {
          final bool isActive = activeLevelFilters.contains(level);
          return FilterChip(
            label: Text(level.toUpperCase()),
            selected: isActive,
            onSelected: (_) => onLevelFilterToggled(level),
            selectedColor: _getColorForLevel(level).withOpacity(0.2),
            backgroundColor: Colors.grey.shade200,
            checkmarkColor: _getColorForLevel(level),
            side: BorderSide(
              color: isActive ? _getColorForLevel(level) : Colors.grey.shade400,
              width: 1.0,
            ),
          );
        }).toList(),
      ),
    );
  }

  Color _getColorForLevel(String level) {
    switch (level.toLowerCase()) {
      case 'error':
        return Colors.red.shade700;
      case 'warn':
        return Colors.orange.shade700;
      case 'info':
        return Colors.blue.shade700;
      case 'debug':
        return Colors.green.shade700;
      default:
        return Colors.grey.shade700;
    }
  }
} 