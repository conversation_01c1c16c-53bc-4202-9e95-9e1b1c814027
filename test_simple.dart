import 'package:tpa_log_reader/kibana_service.dart';

void main() async {
  print('=== 修复后的日期分段查询策略测试 ===');
  print('修复内容:');
  print('1. 2025.6.25-26 现在使用新接口地址 (n9elol.staff.xdf.cn) 但固定索引 p1-weixinshengtai');
  print('2. 2025.6.25之前 使用原始Kibana地址 (kibanalb.staff.xdf.cn) 但固定索引 p1-pro-work-wechat-magic-*');
  print('3. 所有查询都包含 "report task log" 筛选');

  final kibanaService = KibanaService();

  // 测试1: 新接口 (2025.6.27及以后)
  print('\n--- 测试1: 新接口查询 (2025.6.27及以后) ---');
  print('预期: 使用 n9elol.staff.xdf.cn 接口，用户选择的索引');
  try {
    final result1 = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic',
      startTime: DateTime(2025, 6, 28, 10, 0, 0),
      endTime: DateTime(2025, 6, 28, 11, 0, 0),
    );
    print('✅ 新接口查询结果: 总数据量=${result1['logs']?.length ?? 0}, 错误数据量=${result1['errorLogs']?.length ?? 0}');
  } catch (e) {
    print('❌ 新接口查询失败: $e');
  }

  // 测试2: 新接口但固定索引 (2025.6.25-26)
  print('\n--- 测试2: 新接口固定索引查询 (2025.6.25-26) ---');
  print('预期: 使用 n9elol.staff.xdf.cn 接口，固定索引 p1-weixinshengtai');
  try {
    final result2 = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic', // 这个参数会被忽略，强制使用p1-weixinshengtai
      startTime: DateTime(2025, 6, 25, 10, 0, 0),
      endTime: DateTime(2025, 6, 25, 11, 0, 0),
    );
    print('✅ 新接口固定索引查询结果: 总数据量=${result2['logs']?.length ?? 0}, 错误数据量=${result2['errorLogs']?.length ?? 0}');
  } catch (e) {
    print('❌ 新接口固定索引查询失败: $e');
  }

  // 测试3: 原始Kibana查询 (2025.6.25之前)
  print('\n--- 测试3: 原始Kibana查询 (2025.6.25之前) ---');
  print('预期: 使用 kibanalb.staff.xdf.cn 接口，固定索引 p1-pro-work-wechat-magic-*');
  try {
    final result3 = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic', // 这个参数会被忽略，强制使用p1-pro-work-wechat-magic-*
      startTime: DateTime(2025, 6, 23, 10, 0, 0),
      endTime: DateTime(2025, 6, 23, 11, 0, 0),
    );
    print('✅ 原始Kibana查询结果: 总数据量=${result3['logs']?.length ?? 0}, 错误数据量=${result3['errorLogs']?.length ?? 0}');
  } catch (e) {
    print('❌ 原始Kibana查询失败: $e');
  }

  print('\n=== 测试完成 ===');
  print('请检查日志输出，确认:');
  print('1. 测试1使用了 "新接口" 策略');
  print('2. 测试2使用了 "新接口固定索引" 策略');
  print('3. 测试3使用了 "原始Kibana查询" 策略');
  print('4. 所有查询都包含了 "report task log" 筛选');
}
