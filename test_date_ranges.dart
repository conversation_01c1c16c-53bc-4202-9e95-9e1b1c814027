import 'dart:convert';
import 'package:dio/dio.dart';

void main() async {
  print('=== 日期分段查询策略验证测试 ===');
  
  final dio = Dio();
  
  // 测试1: 2025.6.27及以后 - 新接口
  await testNewInterface(dio);
  
  // 测试2: 2025.6.25-26 - 新接口但固定索引
  await testNewInterfaceFixedIndex(dio);
  
  // 测试3: 2025.6.25之前 - 原始Kibana
  await testLegacyKibana(dio);
  
  print('\n=== 所有测试完成 ===');
}

Future<void> testNewInterface(Dio dio) async {
  print('\n--- 测试1: 新接口 (2025.6.27及以后) ---');
  print('接口地址: https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch');
  print('索引: p1-pro-work-wechat-magic');
  print('时间: 2025-06-28');
  
  try {
    // 登录
    await dio.post(
      "https://kibanalb.staff.xdf.cn/api/security/v1/login",
      data: {"username": "p1-k12-h5app-user", "password": "i1ZjyFwv0/"},
    ).catchError((e) => print("登录: $e"));
    
    final startTime = DateTime(2025, 6, 28, 10, 0, 0);
    final endTime = DateTime(2025, 6, 28, 11, 0, 0);
    final startTimeEpoch = startTime.millisecondsSinceEpoch;
    final endTimeEpoch = endTime.millisecondsSinceEpoch;
    
    final searchHeader = {
      "index": "p1-pro-work-wechat-magic",
      "ignore_unavailable": true,
      "preference": endTimeEpoch,
    };
    
    final searchBody = {
      "version": true,
      "size": 10,
      "sort": [{"@timestamp": {"order": "desc", "unmapped_type": "boolean"}}],
      "_source": {"excludes": []},
      "query": {
        "bool": {
          "must": [
            {"match_phrase": {"message": {"query": "report task log"}}},
            {"range": {"@timestamp": {"gte": startTimeEpoch, "lte": endTimeEpoch, "format": "epoch_millis"}}}
          ],
          "filter": [],
          "should": [],
          "must_not": []
        }
      },
      "timeout": "30000ms",
    };
    
    final requestBody = '${jsonEncode(searchHeader)}\n${jsonEncode(searchBody)}\n';
    
    final response = await dio.post(
      "https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch?rest_total_hits_as_int=true&ignore_throttled=true",
      data: requestBody,
      options: Options(contentType: 'application/x-ndjson'),
    );
    
    final result = response.data['responses'][0];
    final hits = result['hits']['hits'] as List;
    print('✅ 新接口测试成功: 返回${hits.length}条数据');
    print('   总命中数: ${result['hits']['total']}');
    print('   查询包含: "report task log" 筛选');
    
  } catch (e) {
    print('❌ 新接口测试失败: $e');
  }
}

Future<void> testNewInterfaceFixedIndex(Dio dio) async {
  print('\n--- 测试2: 新接口固定索引 (2025.6.25-26) ---');
  print('接口地址: https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch');
  print('索引: p1-weixinshengtai (固定)');
  print('时间: 2025-06-25');
  
  try {
    // 登录
    await dio.post(
      "https://kibanalb.staff.xdf.cn/api/security/v1/login",
      data: {"username": "p1-k12-h5app-user", "password": "i1ZjyFwv0/"},
    ).catchError((e) => print("登录: $e"));
    
    final startTime = DateTime(2025, 6, 25, 10, 0, 0);
    final endTime = DateTime(2025, 6, 25, 11, 0, 0);
    final startTimeEpoch = startTime.millisecondsSinceEpoch;
    final endTimeEpoch = endTime.millisecondsSinceEpoch;
    
    final searchHeader = {
      "index": "p1-weixinshengtai", // 固定索引
      "ignore_unavailable": true,
      "preference": endTimeEpoch,
    };
    
    final searchBody = {
      "version": true,
      "size": 10,
      "sort": [{"@timestamp": {"order": "desc", "unmapped_type": "boolean"}}],
      "_source": {"excludes": []},
      "query": {
        "bool": {
          "must": [
            {"match_phrase": {"message": {"query": "report task log"}}},
            {"range": {"@timestamp": {"gte": startTimeEpoch, "lte": endTimeEpoch, "format": "epoch_millis"}}}
          ],
          "filter": [],
          "should": [],
          "must_not": []
        }
      },
      "timeout": "30000ms",
    };
    
    final requestBody = '${jsonEncode(searchHeader)}\n${jsonEncode(searchBody)}\n';
    
    final response = await dio.post(
      "https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch?rest_total_hits_as_int=true&ignore_throttled=true",
      data: requestBody,
      options: Options(contentType: 'application/x-ndjson'),
    );
    
    final result = response.data['responses'][0];
    if (result.containsKey('error')) {
      print('⚠️ 新接口固定索引查询返回错误: ${result['error']}');
    } else {
      final hits = result['hits']['hits'] as List;
      print('✅ 新接口固定索引测试成功: 返回${hits.length}条数据');
      print('   总命中数: ${result['hits']['total']}');
      print('   使用索引: p1-weixinshengtai');
      print('   查询包含: "report task log" 筛选');
    }
    
  } catch (e) {
    print('❌ 新接口固定索引测试失败: $e');
  }
}

Future<void> testLegacyKibana(Dio dio) async {
  print('\n--- 测试3: 原始Kibana (2025.6.25之前) ---');
  print('接口地址: https://kibanalb.staff.xdf.cn/s/weixinshengtai/elasticsearch/_msearch');
  print('索引: p1-pro-work-wechat-magic-* (固定)');
  print('时间: 2025-06-23');
  
  try {
    // 登录
    await dio.post(
      "https://kibanalb.staff.xdf.cn/api/security/v1/login",
      data: {"username": "p1-k12-h5app-user", "password": "i1ZjyFwv0/"},
    ).catchError((e) => print("登录: $e"));
    
    final startTime = DateTime(2025, 6, 23, 10, 0, 0);
    final endTime = DateTime(2025, 6, 23, 11, 0, 0);
    final startTimeEpoch = startTime.millisecondsSinceEpoch;
    final endTimeEpoch = endTime.millisecondsSinceEpoch;
    
    final searchHeader = {
      "index": "p1-pro-work-wechat-magic-*", // 固定索引
      "ignore_unavailable": true,
      "preference": endTimeEpoch,
    };
    
    final searchBody = {
      "version": true,
      "size": 10,
      "sort": [{"@timestamp": {"order": "desc", "unmapped_type": "boolean"}}],
      "_source": {"excludes": []},
      "query": {
        "bool": {
          "must": [
            {"match_phrase": {"message": {"query": "report task log"}}},
            {"range": {"@timestamp": {"gte": startTimeEpoch, "lte": endTimeEpoch, "format": "epoch_millis"}}}
          ],
          "filter": [],
          "should": [],
          "must_not": []
        }
      },
      "timeout": "30000ms",
    };
    
    final requestBody = '${jsonEncode(searchHeader)}\n${jsonEncode(searchBody)}\n';
    
    final response = await dio.post(
      "https://kibanalb.staff.xdf.cn/s/weixinshengtai/elasticsearch/_msearch?rest_total_hits_as_int=true&ignore_throttled=true",
      data: requestBody,
      options: Options(contentType: 'application/x-ndjson'),
    );
    
    final result = response.data['responses'][0];
    if (result.containsKey('error')) {
      print('⚠️ 原始Kibana查询返回错误: ${result['error']}');
    } else {
      final hits = result['hits']['hits'] as List;
      print('✅ 原始Kibana测试成功: 返回${hits.length}条数据');
      print('   总命中数: ${result['hits']['total']}');
      print('   使用索引: p1-pro-work-wechat-magic-*');
      print('   查询包含: "report task log" 筛选');
    }
    
  } catch (e) {
    print('❌ 原始Kibana测试失败: $e');
  }
}
