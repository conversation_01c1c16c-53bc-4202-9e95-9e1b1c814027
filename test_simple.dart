import 'package:tpa_log_reader/kibana_service.dart';

void main() async {
  print('=== 简单测试：验证日期分段查询策略 ===');
  
  final kibanaService = KibanaService();
  
  // 测试1: 新接口 (2025.6.27及以后)
  print('\n--- 测试1: 新接口查询 (2025.6.27及以后) ---');
  try {
    final result1 = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic',
      startTime: DateTime(2025, 6, 28, 10, 0, 0),
      endTime: DateTime(2025, 6, 28, 11, 0, 0),
    );
    print('新接口查询结果: 总数据量=${result1['logs']?.length ?? 0}, 错误数据量=${result1['errorLogs']?.length ?? 0}');
  } catch (e) {
    print('新接口查询失败: $e');
  }
  
  // 测试2: p1-weixinshengtai大索引 (2025.6.25-26)
  print('\n--- 测试2: p1-weixinshengtai大索引查询 (2025.6.25-26) ---');
  try {
    final result2 = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic',
      startTime: DateTime(2025, 6, 25, 10, 0, 0),
      endTime: DateTime(2025, 6, 25, 11, 0, 0),
    );
    print('p1-weixinshengtai查询结果: 总数据量=${result2['logs']?.length ?? 0}, 错误数据量=${result2['errorLogs']?.length ?? 0}');
  } catch (e) {
    print('p1-weixinshengtai查询失败: $e');
  }
  
  // 测试3: 原始Kibana查询 (2025.6.25之前)
  print('\n--- 测试3: 原始Kibana查询 (2025.6.25之前) ---');
  try {
    final result3 = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic',
      startTime: DateTime(2025, 6, 23, 10, 0, 0),
      endTime: DateTime(2025, 6, 23, 11, 0, 0),
    );
    print('原始Kibana查询结果: 总数据量=${result3['logs']?.length ?? 0}, 错误数据量=${result3['errorLogs']?.length ?? 0}');
  } catch (e) {
    print('原始Kibana查询失败: $e');
  }
  
  print('\n=== 测试完成 ===');
}
