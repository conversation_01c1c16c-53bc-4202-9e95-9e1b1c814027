import 'package:shared_preferences/shared_preferences.dart';
import '../models/history_item.dart';

class HistoryService {
  static const _historyKey = 'log_history';

  Future<List<HistoryItem>> getHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getStringList(_historyKey) ?? [];
    
    final historyItems = historyJson
        .map((item) => HistoryItem.fromJson(item))
        .toList();
        
    // Sort by most recently accessed
    historyItems.sort((a, b) => b.lastAccessed.compareTo(a.lastAccessed));
    
    return historyItems;
  }

  Future<void> addHistoryItem(HistoryItem newItem) async {
    final prefs = await SharedPreferences.getInstance();
    final history = await getHistory();

    // Remove old entry if it exists, to be replaced with the new one
    history.removeWhere((item) => item.path == newItem.path);
    history.add(newItem);
    
    // Sort again to ensure the latest is on top if needed, though getHistory already sorts.
    history.sort((a, b) => b.lastAccessed.compareTo(a.lastAccessed));

    final historyJson = history.map((item) => item.toJson()).toList();
    await prefs.setStringList(_historyKey, historyJson);
  }

  Future<void> removeHistoryItem(String path) async {
    final prefs = await SharedPreferences.getInstance();
    final history = await getHistory();
    
    history.removeWhere((item) => item.path == path);
    
    final historyJson = history.map((item) => item.toJson()).toList();
    await prefs.setStringList(_historyKey, historyJson);
  }
} 