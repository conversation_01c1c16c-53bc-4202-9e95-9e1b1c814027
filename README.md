# TPA Log Reader

脚本日志分析器，支持本地压缩包分析和在线Kibana日志查询。

## 📖 项目简介

TPA Log Reader 是一款日志分析工具，提供本地ZIP压缩包分析和在线Kibana日志查询两种模式。通过直观的时间轴界面和智能的错误检测，帮助开发者快速定位和分析脚本执行过程中的问题。

## ✨ 功能特性

### 🗂️ 本地日志压缩包分析器
- ✅ **ZIP压缩包自动解压和解析** - 支持拖拽上传，自动识别日志结构
- ✅ **按日期和会话组织日志数据** - 智能分组，便于查找特定时间段的日志
- ✅ **多种日志类型分类** - 设备信息、步骤、指令、详细日志、错误日志
- ✅ **智能错误检测和标识** - 自动识别异常退出、错误代码、异常信息
- ✅ **图片文件自动识别和展示** - 支持截图预览，便于问题复现
- ✅ **历史记录管理** - 自动保存分析历史，支持快速重新打开
- ✅ **日志内容详细展示** - JSON格式化显示，支持复制和导出

### 🌐 在线Kibana日志查询
- ✅ **连接Kibana服务获取实时日志** - 支持企业内部Kibana集群
- ✅ **时间轴可视化展示** - 直观的时间线布局，支持桌面和移动端适配
- ✅ **高级搜索功能** - 支持字段搜索、关键词过滤、复合查询
- ✅ **智能查询解析** - 自动识别email、platform等字段查询
- ✅ **实时数据统计** - 显示总数据条数和错误数据统计
- ✅ **详情页面导航** - 点击卡片跳转到专门的详情页面，与本地分析器保持一致的交互体验
- 🆕 **日期分段查询** - 根据日期范围自动选择最优查询策略，支持新接口、大索引和原始Kibana查询

## 🏗️ 项目架构

### 核心模块
```
lib/
├── features/
│   └── local_log/              # 本地日志分析功能模块
│       ├── models/            # 数据模型 (LogSession, HistoryItem)
│       ├── services/          # 业务服务 (LogParser, HistoryService)
│       └── views/             # 界面视图 (LocalLogPage)
├── models/                    # 全局数据模型
│   ├── log_entry.dart        # Kibana日志条目模型
│   ├── filter.dart           # 搜索过滤器模型
│   └── query_ast.dart        # 查询语法树模型
├── pages/                     # 主要页面
│   ├── log_viewer_page.dart  # Kibana日志查看器主页
│   └── log_detail_page.dart  # 日志详情页面
├── widgets/                   # 可复用UI组件
│   ├── timeline_log_item.dart # 时间轴日志卡片
│   ├── message_content_view.dart # 消息内容展示
│   ├── log_search_bar.dart   # 搜索栏组件
│   └── time_range_selector.dart # 时间范围选择器
├── utils/                     # 工具类和辅助函数
│   ├── query_parser.dart     # 查询语句解析器
│   ├── formatters.dart       # 时间和数据格式化
│   └── app_colors.dart       # 应用配色方案
├── kibana_service.dart       # Kibana API服务
└── main.dart                 # 应用程序入口
```

### 技术架构特点
- **模块化设计**: 本地分析和在线查询功能独立模块化
- **响应式布局**: 支持桌面端和移动端自适应布局
- **状态管理**: 基于StatefulWidget的轻量级状态管理
- **数据解析**: 支持JSON日志解析和Kibana Elasticsearch查询
- **错误处理**: 完善的异常捕获和用户友好的错误提示

## 🚀 快速开始

### 📋 环境要求
- **Flutter SDK**: >= 3.4.1
- **Dart SDK**: >= 3.4.1
- **支持平台**: iOS, Android, macOS
- **开发环境**: VS Code / Android Studio / IntelliJ IDEA

### 📦 安装和运行

#### 1. 克隆项目
```bash
git clone <repository-url>
cd tpa_log_reader
```

#### 2. 安装依赖
```bash
flutter pub get
```

#### 3. 运行应用

**macOS 桌面端 (推荐)**
```bash
flutter run -d macos
```

**iOS 模拟器**
```bash
# 查看可用设备
flutter devices
# 运行到iOS模拟器
flutter run -d ios
```

**Android 设备/模拟器**
```bash
# 查看可用设备
flutter devices
# 运行到Android设备
flutter run -d android
```

#### 4. 运行测试
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
dart test test/log_error_detection_test.dart
```

## 🗓️ 日期分段查询策略

应用程序根据查询的日期范围自动选择最优的查询策略：

### 📅 策略分类

| 日期范围 | 策略 | 接口地址 | 索引 | 说明 |
|---------|------|----------|------|------|
| **2025.6.27及以后** | 🆕 新接口 | `n9elol.staff.xdf.cn/api/n9e/proxy/29` | `p1-pro-work-wechat-magic` | 最新优化接口 |
| **2025.6.25-26** | 🔍 大索引 | `kibanalb.staff.xdf.cn/s/weixinshengtai` | `p1-weixinshengtai` | 过渡期专用 |
| **2025.6.25之前** | 📚 原始Kibana | `kibanalb.staff.xdf.cn/s/weixinshengtai` | 用户选择 | 历史数据查询 |

### 🔄 自动切换逻辑

```dart
if (startTime >= 2025-06-27) {
    // 使用新接口 - 性能最优
} else if (startTime >= 2025-06-25) {
    // 使用p1-weixinshengtai大索引 - 过渡期数据
} else {
    // 使用原始Kibana查询 - 历史数据
}
```

### 📊 用户体验

- ✅ **透明切换**: 用户无需手动选择，系统自动处理
- ✅ **统一界面**: 不同策略返回相同格式数据
- ✅ **日志提示**: 控制台显示当前使用的查询策略
- ✅ **完整功能**: 所有策略都支持搜索、筛选、分页等功能

详细信息请参考：[日期策略文档](docs/DATE_STRATEGY.md)

## 📦 打包部署

### Android APK 打包
```bash
# 构建 Release APK
flutter build apk --release

# 构建 App Bundle (推荐用于 Google Play)
flutter build appbundle --release

# 输出位置
# APK: build/app/outputs/flutter-apk/app-release.apk (约 24MB)
# AAB: build/app/outputs/bundle/release/app-release.aab
```

**✅ 已完成打包**: Android Release APK 已生成，文件大小约 24MB

### iOS IPA 打包
```bash
# 构建 iOS Release 版本
flutter build ios --release

# 使用 Xcode 进行签名和打包
# 1. 在 Xcode 中打开 ios/Runner.xcworkspace
# 2. 选择 Product > Archive
# 3. 在 Organizer 中导出 IPA 文件
```

### macOS 应用打包
```bash
# 构建 macOS Release 版本
flutter build macos --release

# 输出位置: build/macos/Build/Products/Release/tpa_log_reader.app (约 55MB)

# 创建 DMG 安装包 (可选)
# 需要安装 create-dmg: brew install create-dmg
create-dmg \
  --volname "TPA Log Reader" \
  --window-pos 200 120 \
  --window-size 600 300 \
  --icon-size 100 \
  --icon "tpa_log_reader.app" 175 120 \
  --hide-extension "tpa_log_reader.app" \
  --app-drop-link 425 120 \
  "TPA-Log-Reader.dmg" \
  "build/macos/Build/Products/Release/"
```

**✅ 已完成打包**: macOS Release 应用已生成，文件大小约 55MB

### 🛠️ 一键构建脚本
为了简化构建过程，提供了自动化构建脚本：

```bash
# 运行一键构建脚本
./scripts/build_all.sh
```

该脚本会自动：
- ✅ 检查Flutter环境
- ✅ 清理之前的构建
- ✅ 构建Android APK
- ✅ 构建macOS应用（仅在macOS系统上）
- ✅ **自动创建macOS DMG安装包**
- ✅ 自动安装create-dmg工具（如未安装）
- ✅ 显示构建结果和文件位置

**✅ 已测试验证**: 脚本已完成测试，能够成功构建所有目标平台

### 📋 打包结果总结
- **Android APK**: `build/app/outputs/flutter-apk/app-release.apk` (24.0MB)
- **macOS App**: `build/macos/Build/Products/Release/tpa_log_reader.app` (54.8MB)
- **macOS DMG**: `build/macos/Build/Products/Release/TPA-Log-Reader-v1.1.0.dmg` (20MB) - **优化后**
- **支持架构**: ARM64 + x86_64 (Universal Binary for macOS)
- **最低系统要求**:
  - Android: API Level 21 (Android 5.0)
  - macOS: macOS 10.14 Mojave

### 🎯 DMG 优化特性
- ✅ **精简内容**: DMG中只包含应用程序和Applications链接
- ✅ **统一位置**: DMG文件与.app文件位于同一目录
- ✅ **大幅压缩**: 文件大小从177MB优化到20MB
- ✅ **专业体验**: 支持拖拽安装，用户体验友好

### 📖 详细构建文档
完整的构建指南请参考: [构建指南文档](docs/BUILD_GUIDE.md)

## 已知问题和改进计划

### 高优先级问题（需要立即解决）
- [ ] **改善错误信息提示**：技术性错误信息对普通用户不够友好，需要提供更清晰的错误描述和解决建议
- [ ] **添加进度指示器**：大文件处理时缺乏进度反馈，用户体验不佳
- [ ] **实现基本搜索功能**：本地日志分析器缺乏全文搜索能力
- [ ] **完善加载状态指示**：操作反馈不及时，用户不知道当前处理状态

### 中优先级问题（短期改进）
- [ ] **实现分页加载**：大型日志文件一次性加载导致内存压力和UI阻塞
- [ ] **添加数据导出功能**：无法导出过滤结果和生成分析报告
- [ ] **改进临时文件管理**：临时文件清理不完善，可能导致磁盘空间浪费
- [ ] **增加配置选项**：缺乏用户自定义显示偏好和配置管理
- [ ] **时间范围过滤**：本地日志分析器缺乏按时间段过滤功能
- [ ] **高级过滤选项**：只能按类型过滤，缺乏组合条件过滤

### 低优先级问题（长期改进）
- [ ] **重构代码架构**：业务逻辑与UI耦合，错误判断逻辑分散，需要改善代码结构
- [ ] **添加高级分析功能**：缺乏趋势分析、性能指标分析等深度分析功能
- [ ] **支持多种文件格式**：目前只支持ZIP格式和特定JSON格式，扩展性有限
- [ ] **实现插件化架构**：支持自定义日志解析器和分析器
- [ ] **完善测试覆盖率**：缺乏全面的单元测试和集成测试
- [ ] **性能优化**：使用Isolate进行后台解析，实现虚拟滚动列表

### 功能增强建议
- [ ] **统计分析面板**：添加错误统计、执行时间分析、成功率统计等
- [ ] **日志对比功能**：支持多个会话之间的日志对比
- [ ] **自动化报告生成**：定期生成日志分析报告
- [ ] **实时监控模式**：支持实时监控正在执行的脚本
- [ ] **多语言支持**：支持国际化
- [ ] **主题定制**：支持深色模式和主题切换

## 技术栈

- **框架**: Flutter 3.x
- **语言**: Dart 3.x
- **状态管理**: StatefulWidget (计划迁移到Riverpod/Bloc)
- **网络请求**: Dio
- **本地存储**: SharedPreferences
- **文件处理**: archive, file_picker, path_provider
- **UI组件**: Material Design 3
- **测试**: flutter_test, test

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. **Fork 项目** - 点击右上角的 Fork 按钮
2. **创建功能分支** - `git checkout -b feature/AmazingFeature`
3. **提交更改** - `git commit -m 'Add some AmazingFeature'`
4. **推送到分支** - `git push origin feature/AmazingFeature`
5. **开启 Pull Request** - 在 GitHub 上创建 PR

### 开发规范
- 遵循 Dart 官方代码风格指南
- 为新功能添加相应的测试用例
- 更新相关文档和注释
- 确保所有测试通过后再提交 PR

## 📄 许可证

本项目为私有项目，仅供内部使用。未经授权不得复制、分发或修改。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- **项目维护者**: 开发团队
- **技术支持**: 请在项目中创建 Issue
- **功能建议**: 欢迎提交 Feature Request

## 🙏 致谢

感谢以下开源项目和技术栈的支持：
- [Flutter](https://flutter.dev/) - 跨平台UI框架
- [Dio](https://pub.dev/packages/dio) - HTTP客户端
- [Material Design](https://material.io/) - 设计系统
- [Elasticsearch](https://www.elastic.co/) - 搜索和分析引擎

## 📱 使用指南

### 本地日志分析
1. **上传压缩包**: 点击右上角的压缩包图标，选择ZIP格式的日志文件
2. **选择会话**: 在筛选卡片中选择日期和具体的执行会话
3. **查看日志**: 支持按类型过滤（设备信息、步骤、指令、日志、错误）
4. **查看详情**: 点击日志条目查看JSON格式的详细信息
5. **查看截图**: 如果日志包含截图，会自动在图片卡片中显示

### 在线日志查询
1. **设置时间范围**: 使用时间选择器设定查询的开始和结束时间
2. **输入搜索条件**: 支持多种搜索语法
   - `email: <EMAIL>` - 搜索特定邮箱
   - `platform: ios` - 搜索特定平台
   - `关键词` - 全文搜索
3. **查看时间轴**: 日志按时间顺序在时间轴上展示
4. **查看统计**: 每个卡片显示总数据条数和错误数据条数
5. **进入详情**: 点击卡片进入详情页面，支持按类型过滤查看

## 📊 更新日志

### v1.1.0 (当前版本) - 2025-07-01
- ✅ **重大更新**: 支持查看日志压缩包, 兼容新版本查询接口
- ✅ **新功能**: 实现时间轴卡片点击跳转到专门的详情页面
- ✅ **新功能**: 添加实时数据统计显示（总数据条数和错误数据条数）
- ✅ **优化**: 统一日志详情页面与本地分析器的交互体验和视觉设计

### v1.0.0 - 2025-06-19
- ✅ 实现在线Kibana日志查询
- - ✅ **优化**: 完善搜索功能，支持email、platform等字段的精确搜索
