/// 格式化时间用于时间线显示。
/// 返回一个包含 'time' 和 'date' 的 Map。
Map<String, String> formatTimeForTimeline(String timestamp) {
  if (timestamp.isEmpty) return {'time': 'N/A', 'date': 'N/A'};
  try {
    // 解析原始UTC时间
    final utcDateTime = DateTime.parse(timestamp).toUtc();
    
    // 转换为东八区时间（UTC+8）
    final east8DateTime = utcDateTime.add(const Duration(hours: 8));
    
    // 格式化输出
    final milliseconds = east8DateTime.millisecond.toString().padLeft(3, '0');
    return {
      'time':
          '${east8DateTime.hour.toString().padLeft(2, '0')}:${east8DateTime.minute.toString().padLeft(2, '0')}:${east8DateTime.second.toString().padLeft(2, '0')}.$milliseconds',
      'date':
          '${east8DateTime.year}-${east8DateTime.month.toString().padLeft(2, '0')}-${east8DateTime.day.toString().padLeft(2, '0')}'
    };
  } catch (e) {
    return {'time': 'Invalid Time', 'date': 'Invalid Date'};
  }
}