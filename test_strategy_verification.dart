import 'package:tpa_log_reader/kibana_service.dart';

void main() async {
  print('=== 日期策略路由验证测试 ===');
  print('目标：验证不同日期范围是否正确路由到对应的查询方法');
  
  final kibanaService = KibanaService();
  
  // 测试1: 2025.6.28 - 应该使用新接口
  print('\n--- 测试1: 2025.6.28 (应该使用新接口) ---');
  try {
    await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic',
      startTime: DateTime(2025, 6, 28, 10, 0, 0),
      endTime: DateTime(2025, 6, 28, 11, 0, 0),
    );
  } catch (e) {
    print('预期的网络错误: $e');
  }
  
  // 测试2: 2025.6.25 - 应该使用新接口但固定索引
  print('\n--- 测试2: 2025.6.25 (应该使用新接口固定索引) ---');
  try {
    await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic', // 这个会被忽略
      startTime: DateTime(2025, 6, 25, 10, 0, 0),
      endTime: DateTime(2025, 6, 25, 11, 0, 0),
    );
  } catch (e) {
    print('预期的网络错误: $e');
  }
  
  // 测试3: 2025.6.23 - 应该使用原始Kibana
  print('\n--- 测试3: 2025.6.23 (应该使用原始Kibana) ---');
  try {
    await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic', // 这个会被忽略
      startTime: DateTime(2025, 6, 23, 10, 0, 0),
      endTime: DateTime(2025, 6, 23, 11, 0, 0),
    );
  } catch (e) {
    print('预期的网络错误: $e');
  }
  
  print('\n=== 验证完成 ===');
  print('请检查上面的日志输出，确认:');
  print('1. 测试1显示 "使用策略: 新接口 (2025.6.27及以后)"');
  print('2. 测试2显示 "使用策略: 新接口固定索引 (2025.6.25-26)"');
  print('3. 测试3显示 "使用策略: 原始Kibana查询 (2025.6.25之前)"');
}
