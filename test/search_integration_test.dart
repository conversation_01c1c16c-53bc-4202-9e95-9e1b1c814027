import 'package:flutter_test/flutter_test.dart';
import '../lib/kibana_service.dart';
import '../lib/models/query_ast.dart';

void main() {
  group('KibanaService 搜索集成测试', () {
    late KibanaService kibanaService;

    setUp(() {
      kibanaService = KibanaService();
    });

    group('查询构建测试', () {
      test('email字段应该生成正确的查询格式', () {
        final emailQuery = FilterNode(key: 'email', operator: ':', value: '<EMAIL>');
        final result = kibanaService.buildQueryFromAstForTesting(emailQuery);
        
        print('Email查询结果: $result');
        
        expect(result['match_phrase'], isNotNull);
        expect(result['match_phrase']['message']['query'], equals('email=<EMAIL>'));
      });

      test('appVersion字段应该生成正确的查询格式', () {
        final appVersionQuery = FilterNode(key: 'appVersion', operator: ':', value: '1.3.2');
        final result = kibanaService.buildQueryFromAstForTesting(appVersionQuery);
        
        print('AppVersion查询结果: $result');
        
        expect(result['match_phrase'], isNotNull);
        expect(result['match_phrase']['message']['query'], equals('"appVersion":"1.3.2"'));
      });

      test('platformName字段应该生成正确的查询格式', () {
        final platformQuery = FilterNode(key: 'platformName', operator: ':', value: 'macos');
        final result = kibanaService.buildQueryFromAstForTesting(platformQuery);
        
        print('PlatformName查询结果: $result');
        
        expect(result['match_phrase'], isNotNull);
        expect(result['match_phrase']['message']['query'], equals('"platformName":"macos"'));
      });

      test('taskId字段应该生成正确的查询格式', () {
        final taskIdQuery = FilterNode(key: 'taskId', operator: ':', value: '5a57d82c144b49c98345fbe5469fadc8');
        final result = kibanaService.buildQueryFromAstForTesting(taskIdQuery);
        
        print('TaskId查询结果: $result');
        
        expect(result['match_phrase'], isNotNull);
        expect(result['match_phrase']['message']['query'], equals('"taskId":"5a57d82c144b49c98345fbe5469fadc8"'));
      });

      test('pythonVersion字段应该生成正确的查询格式', () {
        final pythonVersionQuery = FilterNode(key: 'pythonVersion', operator: ':', value: '1.1.1');
        final result = kibanaService.buildQueryFromAstForTesting(pythonVersionQuery);
        
        print('PythonVersion查询结果: $result');
        
        expect(result['match_phrase'], isNotNull);
        expect(result['match_phrase']['message']['query'], equals('"pythonVersion":"1.1.1"'));
      });

      test('systemName字段应该生成正确的查询格式', () {
        final systemNameQuery = FilterNode(key: 'systemName', operator: ':', value: 'macOS');
        final result = kibanaService.buildQueryFromAstForTesting(systemNameQuery);
        
        print('SystemName查询结果: $result');
        
        expect(result['match_phrase'], isNotNull);
        expect(result['match_phrase']['message']['query'], equals('"systemName":"macOS"'));
      });
    });

    group('复合查询测试', () {
      test('AND查询应该正确组合多个条件', () {
        final emailQuery = FilterNode(key: 'email', operator: ':', value: '<EMAIL>');
        final appVersionQuery = FilterNode(key: 'appVersion', operator: ':', value: '1.3.2');
        final andQuery = AndNode(emailQuery, appVersionQuery);
        
        final result = kibanaService.buildQueryFromAstForTesting(andQuery);
        
        print('AND查询结果: $result');
        
        expect(result['bool'], isNotNull);
        expect(result['bool']['must'], isNotNull);
        expect(result['bool']['must'].length, equals(2));
      });

      test('OR查询应该正确组合多个条件', () {
        final emailQuery = FilterNode(key: 'email', operator: ':', value: '<EMAIL>');
        final emailQuery2 = FilterNode(key: 'email', operator: ':', value: '<EMAIL>');
        final orQuery = OrNode(emailQuery, emailQuery2);
        
        final result = kibanaService.buildQueryFromAstForTesting(orQuery);
        
        print('OR查询结果: $result');
        
        expect(result['bool'], isNotNull);
        expect(result['bool']['should'], isNotNull);
        expect(result['bool']['should'].length, equals(2));
        expect(result['bool']['minimum_should_match'], equals(1));
      });
    });

    group('实际API测试', () {
      test('测试实际的搜索请求 - email搜索', () async {
        try {
          final emailQuery = FilterNode(key: 'email', operator: ':', value: '<EMAIL>');
          
          final result = await kibanaService.fetchLogs(
            queryAst: emailQuery,
            startTime: DateTime.now().subtract(Duration(days: 1)),
            endTime: DateTime.now(),
            indexPattern: 'p1-pro-work-wechat-magic',
            pageSize: 10,
          );

          print('Email搜索结果: ${result['total']} 条记录');
          if (result['logs'].isNotEmpty) {
            print('第一条记录: ${result['logs'][0]}');
          }
          
          expect(result, isNotNull);
          expect(result['logs'], isNotNull);
          expect(result['total'], isNotNull);
        } catch (e) {
          print('Email搜索测试失败: $e');
          // 在测试环境中，网络请求可能会失败，这是正常的
        }
      }, timeout: Timeout(Duration(seconds: 30)));

      test('测试实际的搜索请求 - appVersion搜索', () async {
        try {
          final appVersionQuery = FilterNode(key: 'appVersion', operator: ':', value: '1.3.2');
          
          final result = await kibanaService.fetchLogs(
            queryAst: appVersionQuery,
            startTime: DateTime.now().subtract(Duration(days: 1)),
            endTime: DateTime.now(),
            indexPattern: 'p1-pro-work-wechat-magic',
            pageSize: 10,
          );

          print('AppVersion搜索结果: ${result['total']} 条记录');
          if (result['logs'].isNotEmpty) {
            print('第一条记录: ${result['logs'][0]}');
          }
          
          expect(result, isNotNull);
          expect(result['logs'], isNotNull);
          expect(result['total'], isNotNull);
        } catch (e) {
          print('AppVersion搜索测试失败: $e');
          // 在测试环境中，网络请求可能会失败，这是正常的
        }
      }, timeout: Timeout(Duration(seconds: 30)));

      test('测试时间范围搜索', () async {
        try {
          // 使用你提供的示例时间戳
          final startTime = DateTime.fromMillisecondsSinceEpoch(1751212600000);
          final endTime = DateTime.fromMillisecondsSinceEpoch(1751212700000);
          
          final result = await kibanaService.fetchLogs(
            startTime: startTime,
            endTime: endTime,
            indexPattern: 'p1-pro-work-wechat-magic',
            pageSize: 10,
          );

          print('时间范围搜索结果: ${result['total']} 条记录');
          if (result['logs'].isNotEmpty) {
            print('第一条记录时间戳: ${result['logs'][0]['_source']['@timestamp']}');
          }
          
          expect(result, isNotNull);
          expect(result['logs'], isNotNull);
          expect(result['total'], isNotNull);
        } catch (e) {
          print('时间范围搜索测试失败: $e');
          // 在测试环境中，网络请求可能会失败，这是正常的
        }
      }, timeout: Timeout(Duration(seconds: 30)));
    });
  });
}
