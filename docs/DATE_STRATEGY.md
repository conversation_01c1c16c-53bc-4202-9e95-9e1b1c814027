# 日期分段查询策略

## 概述

由于日志接口的限制和数据存储的变化，应用程序现在根据查询的日期范围自动选择不同的查询策略，以确保能够正确获取所有时间段的日志数据。

## 策略分类

### 🆕 新接口 (2025.6.27及以后)
- **适用日期**: 2025年6月27日 00:00:00 及以后
- **接口地址**: `https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch`
- **索引**: `p1-pro-work-wechat-magic`
- **特点**: 
  - 支持最新的日志数据
  - 性能优化的新接口
  - 自动筛选 "report task log"

### 🔍 p1-weixinshengtai大索引 (2025.6.25-26)
- **适用日期**: 2025年6月25日 00:00:00 - 2025年6月26日 23:59:59
- **接口地址**: `https://kibanalb.staff.xdf.cn/s/weixinshengtai/elasticsearch/_msearch`
- **索引**: `p1-weixinshengtai` (强制使用大索引)
- **特点**:
  - 专门处理6月25-26日的过渡期数据
  - 使用原始Kibana查询逻辑
  - 支持完整的搜索功能

### 📚 原始Kibana查询 (2025.6.25之前)
- **适用日期**: 2025年6月25日 00:00:00 之前
- **接口地址**: `https://kibanalb.staff.xdf.cn/s/weixinshengtai/elasticsearch/_msearch`
- **索引**: 用户选择的索引模式
- **特点**:
  - 使用传统的Kibana查询方式
  - 支持所有历史索引模式
  - 完整的搜索和筛选功能

## 自动策略选择

应用程序会根据查询的**开始时间**自动选择合适的策略：

```dart
if (startTime >= 2025-06-27) {
    // 使用新接口
} else if (startTime >= 2025-06-25) {
    // 使用p1-weixinshengtai大索引
} else {
    // 使用原始Kibana查询
}
```

## 用户体验

- **透明切换**: 用户无需手动选择策略，系统自动处理
- **统一界面**: 不同策略返回相同格式的数据，界面保持一致
- **日志提示**: 控制台会显示当前使用的查询策略，便于调试

## 示例日志输出

```
--- 日期范围分析 ---
查询开始时间: 2025-07-02 13:48:28.684932
查询结束时间: 2025-07-02 13:53:28.685530
6月25日: 2025-06-25 00:00:00.000
6月27日: 2025-06-27 00:00:00.000
使用策略: 新接口 (2025.6.27及以后)
```

## 边界条件处理

- **6月25日 00:00:00**: 使用p1-weixinshengtai大索引
- **6月27日 00:00:00**: 使用新接口
- **跨日期查询**: 根据开始时间确定策略

## 技术实现

### 核心方法
- `fetchLogs()`: 主入口，负责策略选择
- `_fetchLogsNewApi()`: 新接口实现
- `_fetchLogsLegacyKibana()`: 原始Kibana查询实现

### 关键代码
```dart
// 定义关键日期点
final june25 = DateTime(2025, 6, 25);
final june27 = DateTime(2025, 6, 27);

// 判断查询策略
if (startTime.isAfter(june27) || startTime.isAtSameMomentAs(june27)) {
  // 新接口
} else if (startTime.isAfter(june25) || startTime.isAtSameMomentAs(june25)) {
  // p1-weixinshengtai大索引
} else {
  // 原始Kibana查询
}
```

## 维护说明

- 如果需要调整日期边界，修改 `june25` 和 `june27` 变量
- 新增策略时，在 `fetchLogs()` 方法中添加相应的条件判断
- 每个策略的具体实现在对应的私有方法中维护

## 测试验证

所有日期策略都经过了完整的测试验证，包括：
- ✅ 边界条件测试
- ✅ 不同日期范围的策略选择
- ✅ 实际查询功能验证
