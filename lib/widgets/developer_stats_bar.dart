import 'package:flutter/material.dart';
import '../models/log_entry.dart';

class DeveloperStatsBar extends StatelessWidget {
  final List<LogEntry> logs;
  final VoidCallback onRefresh;
  final int pageSize;
  final List<int> availablePageSizes;
  final ValueChanged<int?> onPageSizeChanged;

  const DeveloperStatsBar({
    super.key,
    required this.logs,
    required this.onRefresh,
    required this.pageSize,
    required this.availablePageSizes,
    required this.onPageSizeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final successCount = logs.where((log) => log.status.startsWith('2')).length;
    final errorCount = logs.where((log) => log.status.startsWith('4') || log.status.startsWith('5')).length;
    final slowRequests = logs.where((log) {
      final time = double.tryParse(log.responseTime) ?? 0;
      return time > 1.0;
    }).length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Icon(Icons.developer_mode, color: Colors.blue.shade600, size: 18),
          const SizedBox(width: 8),
          Text('${logs.length} 条日志', style: const TextStyle(fontWeight: FontWeight.bold)),
          // const SizedBox(width: 16),
          // _buildQuickStat('✅', successCount, Colors.green.shade700),
          // const SizedBox(width: 12),
          // _buildQuickStat('❌', errorCount, Colors.red.shade700),
          // const SizedBox(width: 12),
          // _buildQuickStat('🐌', slowRequests, Colors.orange.shade700),
          const Spacer(),
          Row(
            children: [
              SizedBox(
                height: 30,
                child: DropdownButton<int>(
                  value: pageSize,
                  items: availablePageSizes.map((size) {
                    return DropdownMenuItem<int>(
                      value: size,
                      child: Text('$size / 页'),
                    );
                  }).toList(),
                  onChanged: onPageSizeChanged,
                  underline: Container(),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.refresh, size: 18),
                onPressed: onRefresh,
                tooltip: '刷新',
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildQuickStat(String icon, int count, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(icon, style: const TextStyle(fontSize: 12)),
        const SizedBox(width: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}