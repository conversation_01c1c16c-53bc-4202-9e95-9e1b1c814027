import 'lib/kibana_service.dart';

/// 专门测试6月25-26日查询的脚本
/// 用于验证和调试这个特定时间段的查询问题
void main() async {
  
  print('=== 6月25-26日查询专项测试 ===');
  
  final kibanaService = KibanaService();
  
  // 测试1: 6月25日查询
  await testJune25Query(kibanaService);
  
  // 测试2: 6月26日查询  
  await testJune26Query(kibanaService);
  
  // 测试3: 跨6月25-26日查询
  await testJune25To26Query(kibanaService);
  
  print('\n=== 测试完成 ===');
}

/// 测试6月25日的查询
Future<void> testJune25Query(KibanaService kibanaService) async {
  print('\n--- 测试1: 6月25日查询 ---');
  
  final startTime = DateTime(2025, 6, 25, 9, 0, 0);  // 2025-06-25 09:00:00
  final endTime = DateTime(2025, 6, 25, 18, 0, 0);   // 2025-06-25 18:00:00
  
  print('查询时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
  print('预期策略: 新接口固定索引 (2025.6.25-26)');
  print('预期索引: p1-weixinshengtai');
  print('预期接口: https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch');
  
  try {
    final result = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic', // 这个会被强制替换为p1-weixinshengtai
      startTime: startTime,
      endTime: endTime,
    );
    
    print('\n查询结果:');
    print('- 总数据量: ${result['logs']?.length ?? 0}');
    print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
    print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
    
    if ((result['logs']?.length ?? 0) > 0) {
      print('✅ 6月25日查询成功，找到数据');
      
      // 显示第一条数据的详细信息
      final firstLog = result['logs'][0];
      print('第一条数据示例:');
      print('- 时间戳: ${firstLog.timestamp}');
      print('- 消息长度: ${firstLog.message?.length ?? 0}');
      print('- 索引: ${firstLog.index}');
    } else {
      print('⚠️ 6月25日查询成功但没有找到数据');
    }
    
  } catch (e) {
    print('❌ 6月25日查询失败: $e');
  }
}

/// 测试6月26日的查询
Future<void> testJune26Query(KibanaService kibanaService) async {
  print('\n--- 测试2: 6月26日查询 ---');
  
  final startTime = DateTime(2025, 6, 26, 9, 0, 0);  // 2025-06-26 09:00:00
  final endTime = DateTime(2025, 6, 26, 18, 0, 0);   // 2025-06-26 18:00:00
  
  print('查询时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
  print('预期策略: 新接口固定索引 (2025.6.25-26)');
  print('预期索引: p1-weixinshengtai');
  print('预期接口: https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch');
  
  try {
    final result = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic', // 这个会被强制替换为p1-weixinshengtai
      startTime: startTime,
      endTime: endTime,
    );
    
    print('\n查询结果:');
    print('- 总数据量: ${result['logs']?.length ?? 0}');
    print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
    print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
    
    if ((result['logs']?.length ?? 0) > 0) {
      print('✅ 6月26日查询成功，找到数据');
      
      // 显示第一条数据的详细信息
      final firstLog = result['logs'][0];
      print('第一条数据示例:');
      print('- 时间戳: ${firstLog.timestamp}');
      print('- 消息长度: ${firstLog.message?.length ?? 0}');
      print('- 索引: ${firstLog.index}');
    } else {
      print('⚠️ 6月26日查询成功但没有找到数据');
    }
    
  } catch (e) {
    print('❌ 6月26日查询失败: $e');
  }
}

/// 测试跨6月25-26日的查询
Future<void> testJune25To26Query(KibanaService kibanaService) async {
  print('\n--- 测试3: 跨6月25-26日查询 ---');
  
  final startTime = DateTime(2025, 6, 25, 15, 0, 0); // 2025-06-25 15:00:00
  final endTime = DateTime(2025, 6, 26, 15, 0, 0);   // 2025-06-26 15:00:00
  
  print('查询时间范围: ${startTime.toString()} 到 ${endTime.toString()}');
  print('预期策略: 新接口固定索引 (根据开始时间2025.6.25选择)');
  print('预期索引: p1-weixinshengtai');
  print('预期接口: https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch');
  
  try {
    final result = await kibanaService.fetchLogs(
      indexPattern: 'p1-pro-work-wechat-magic', // 这个会被强制替换为p1-weixinshengtai
      startTime: startTime,
      endTime: endTime,
    );
    
    print('\n查询结果:');
    print('- 总数据量: ${result['logs']?.length ?? 0}');
    print('- 错误数据量: ${result['errorLogs']?.length ?? 0}');
    print('- 是否有数据: ${(result['logs']?.length ?? 0) > 0}');
    
    if ((result['logs']?.length ?? 0) > 0) {
      print('✅ 跨6月25-26日查询成功，找到数据');
      
      // 显示第一条和最后一条数据的时间信息
      final firstLog = result['logs'][0];
      final lastLog = result['logs'][result['logs'].length - 1];
      print('数据时间范围:');
      print('- 最新数据时间戳: ${firstLog.timestamp}');
      print('- 最旧数据时间戳: ${lastLog.timestamp}');
    } else {
      print('⚠️ 跨6月25-26日查询成功但没有找到数据');
    }
    
  } catch (e) {
    print('❌ 跨6月25-26日查询失败: $e');
  }
}
