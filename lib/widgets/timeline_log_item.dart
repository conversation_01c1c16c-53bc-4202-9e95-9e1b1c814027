import 'package:flutter/material.dart';
import '../models/log_entry.dart';
import '../utils/app_colors.dart';
import '../utils/formatters.dart';
import '../pages/log_detail_page.dart';
import 'highlight_chip.dart';
import 'log_details_view.dart';

class TimelineLogItem extends StatefulWidget {
  final LogEntry log;
  final bool isLast;
  final OnFilter onFilter;

  const TimelineLogItem({
    super.key,
    required this.log,
    required this.isLast,
    required this.onFilter,
  });

  @override
  State<TimelineLogItem> createState() => _TimelineLogItemState();
}

class _TimelineLogItemState extends State<TimelineLogItem> {

  // 计算错误数据条数
  int _calculateErrorCount() {
    int errorCount = 0;

    // 检查主日志级别
    if (widget.log.level.toLowerCase().contains('error') ||
        widget.log.level.toLowerCase().contains('err') ||
        widget.log.level.toLowerCase().contains('fatal')) {
      errorCount++;
    }

    // 检查主日志消息内容
    final message = widget.log.message.toLowerCase();
    if (message.contains('error') ||
        message.contains('exception') ||
        message.contains('fail') ||
        message.contains('错误')) {
      errorCount++;
    }

    // 检查解析的字段中是否有错误相关内容
    widget.log.parsedMessage.forEach((key, value) {
      final keyLower = key.toLowerCase();
      final valueLower = value.toLowerCase();

      if (keyLower.contains('error') ||
          keyLower.contains('exception') ||
          keyLower.contains('fail') ||
          valueLower.contains('error') ||
          valueLower.contains('exception') ||
          valueLower.contains('fail') ||
          valueLower.contains('错误')) {
        errorCount++;
      }
    });

    return errorCount;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use a breakpoint to switch layouts. 650 seems reasonable to avoid cramping.
        final bool isMobile = constraints.maxWidth < 650;

        if (isMobile) {
          return _buildMobileLayout(context);
        } else {
          return _buildDesktopLayout(context);
        }
      },
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildTimeColumn(widget.log.timestamp),
          const SizedBox(width: 16),
          _buildTimelineColumn(widget.log.status, widget.isLast, context, showPlatformIcon: true),
          const SizedBox(width: 16),
          Expanded(
            child: _buildLogContent(context, isMobile: false),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    // On mobile, we only show the content card, which takes up the full width.
    return _buildLogContent(context, isMobile: true);
  }

  Widget _buildTimeColumn(String timestamp) {
    final timeData = formatTimeForTimeline(timestamp);
    return SizedBox(
      width: 120,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            timeData['time']!,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
              fontFamily: 'monospace',
            ),
            textAlign: TextAlign.right,
          ),
          const SizedBox(height: 2),
          Text(
            timeData['date']!,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
              fontFamily: 'monospace',
            ),
            textAlign: TextAlign.right,
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineColumn(String status, bool isLast, BuildContext context, {bool showPlatformIcon = true}) {
    // --- 优化：针对特定日志类型显示平台图标和颜色 ---
    final platformInfo = _getPlatformInfo();
    final platformIcon = platformInfo.icon;

    // 优先使用 status，如果为空则尝试使用 log.level
    final displayStatus = status.isNotEmpty ? status : widget.log.level;
    // 如果有平台颜色，则优先使用，否则回退到基于状态的颜色
    final statusColor = platformInfo.color ?? getStatusColor(displayStatus);

    return SizedBox(
      width: 50,
      child: Stack(
        alignment: Alignment.topLeft,
        children: [
          // The line connecting the dots
          if (!isLast)
            Positioned(
              left: 24, // Center of the circle
              top: 0,  // Start from top to draw behind the circle
              bottom: 0,
              child: Container(
                width: 2,
                color: Colors.grey.shade300,
              ),
            ),
          // The status circle
          Container(
            width: 40,
            height: 40,
            margin: const EdgeInsets.only(left: 5),
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
              border: Border.all(color: Theme.of(context).scaffoldBackgroundColor, width: 2),
              boxShadow: [
                BoxShadow(
                  color: statusColor.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              // On mobile, if a platform icon exists, render an empty bubble.
              // Otherwise, show the text status. The platform icon is shown inside the card.
              child: (platformIcon != null && !showPlatformIcon)
                  ? const SizedBox.shrink()
                  : (platformIcon != null && showPlatformIcon)
                      ? Icon(platformIcon, color: Colors.white, size: 18)
                      : Text(
                          displayStatus.isNotEmpty ? displayStatus : '?',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogContent(BuildContext context, {bool isMobile = false}) {
    // 1. 计算日志总结信息
    final int totalDataCount = 1 + widget.log.parsedMessage.length; // 主日志 + 解析字段数量
    final int errorDataCount = _calculateErrorCount();
    final String displayTitle = '总数据: ${totalDataCount}条, 错误数据: ${errorDataCount}条';

    // --- 新增: 仅在移动端布局时获取时间数据 ---
    final timeData = isMobile ? formatTimeForTimeline(widget.log.timestamp) : null;

    // Get platform info for mobile icon
    final platformInfo = _getPlatformInfo();
    final platformIcon = platformInfo.icon;
    final platformColor = platformInfo.color;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          onTap: () {
            // 导航到日志详情页面
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => LogDetailPage(logEntry: widget.log),
              ),
            );
          },
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // --- 新增: 在移动端布局的顶部显示时间 ---
                if (isMobile && timeData != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            if (platformIcon != null)
                              Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: Icon(platformIcon, size: 16, color: platformColor),
                              ),
                            Text(
                              timeData['time']!,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade800,
                                fontFamily: 'monospace',
                              ),
                            ),
                          ],
                        ),
                        Text(
                          timeData['date']!,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade600,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ],
                    ),
                  ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.log.method.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 2.0),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            widget.log.method,
                            style: TextStyle(
                              color: Colors.grey.shade700,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ),
                    if (widget.log.method.isNotEmpty) const SizedBox(width: 8),
                    
                    // 如果没有URI，则此部分留空，让右侧的IP和图标可以对齐
                    if (displayTitle.isNotEmpty)
                      Expanded(
                        child: Text(
                          displayTitle,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    else 
                      const Spacer(),

                    if (widget.log.clientIp.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0, top: 2.0),
                        child: Text(
                          '📍 ${widget.log.clientIp}',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade500,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(left: 4.0),
                      child: Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                
                // 显示关键信息摘要
                if (widget.log.parsedMessage.isNotEmpty)
                   Padding(
                     padding: const EdgeInsets.only(top: 8.0),
                     child: _buildParsedMessageSummary(),
                   ),

                // if (widget.log.highlights.isNotEmpty)
                //   _buildHighlightSection(),

              ],
            ),
          ),
        ),
      ),
    );
  }
  
  ({IconData? icon, Color? color}) _getPlatformInfo() {
    final platform = widget.log.parsedMessage['platformName']?.toLowerCase();
    if (platform != null) {
      if (platform.contains('windows')) {
        return (icon: Icons.desktop_windows, color: const Color(0xFF0078D4)); // Windows brand blue
      } else if (platform.contains('mac')) {
        return (icon: Icons.apple, color: const Color(0xFF000000)); // Apple brand gray
      }
    }
    return (icon: null, color: null);
  }

  Widget _buildParsedMessageSummary() {
    // 优先显示的字段顺序
    const preferredOrder = ['email', 'platformName', 'appVersion', 'systemName'];
    final allKeys = widget.log.parsedMessage.keys.toList();

    // 移除一些不需要在摘要中显示的字段
    // platformName 字段已经显示在图标里了，可以考虑移除
    // allKeys.remove('platformName');

    // 先添加优先字段
    final sortedKeys = preferredOrder.where((key) => allKeys.contains(key)).toList();
    
    // 添加剩余字段
    allKeys.forEach((key) {
      if (!sortedKeys.contains(key)) {
        sortedKeys.add(key);
      }
    });

    return Wrap(
      spacing: 6.0,
      runSpacing: 6.0,
      children: sortedKeys.map((key) {
        final value = widget.log.parsedMessage[key]!;
        final pairString = '$key: $value';
        
        // --- 优化：根据键值对内容生成唯一颜色 ---
        // 对 email 特殊处理，保持其固定颜色
        final bool isEmail = key == 'email';
        final Color chipColor = isEmail 
            ? Colors.blue.shade50 
            : _getBackgroundColorForString(pairString);
        
        final Color textColor = isEmail 
            ? Colors.blue.shade800 
            : _getTextColorForString(pairString);

        return Material(
          color: chipColor,
          borderRadius: BorderRadius.circular(12),
          child: InkWell(
            onTap: () => widget.onFilter(key, value, false),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    fontSize: 10,
                    fontFamily: 'monospace',
                    color: textColor,
                  ),
                  children: [
                    TextSpan(
                      text: '$key: ',
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    TextSpan(text: value),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildHighlightSection() {
    // 过滤掉 'status' 和 'method' 字段
    final filteredHighlights = widget.log.highlights.entries.where((entry) {
      final key = entry.key.replaceAll('.keyword', '');
      return key != 'status' && key != 'method';
    });

    if (filteredHighlights.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Wrap(
        spacing: 4.0,
        runSpacing: 4.0,
        children: filteredHighlights.expand((entry) {
          final fieldName = entry.key;
          final highlightValues = entry.value;
          return highlightValues.map(
            (value) => HighlightChip(
              fieldName: fieldName,
              highlightedValue: value,
            ),
          );
        }).toList(),
      ),
    );
  }

  // --- 新增：根据字符串内容生成确定性颜色的辅助方法 ---
  Color _getBackgroundColorForString(String input) {
    final hash = input.hashCode;
    final hue = (hash % 360).toDouble();
    // 使用 HSL 生成柔和的背景色
    return HSLColor.fromAHSL(1.0, hue, 0.7, 0.90).toColor();
  }

  Color _getTextColorForString(String input) {
    final hash = input.hashCode;
    final hue = (hash % 360).toDouble();
    // 使用相同的色相，但更暗以便阅读
    return HSLColor.fromAHSL(1.0, hue, 0.6, 0.35).toColor();
  }
}