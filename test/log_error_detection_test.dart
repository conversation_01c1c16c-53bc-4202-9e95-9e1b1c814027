import 'package:test/test.dart';

void main() {
  group('日志错误检测测试', () {
    setUp(() {
      // 测试设置
    });

    test('步骤100（脚本完成退出）不应被标记为错误', () {
      final log = {
        'type': 1,
        'step': 100,
        'date': 1750407947586,
        'args': {'code': 0} // 即使包含退出代码也不应视为错误
      };

      // 由于_isErrorLog是私有方法，我们需要通过其他方式测试
      // 这里展示了测试的逻辑，实际实现可能需要重构代码结构
      
      // 预期结果：不是错误日志
      expect(isErrorLogForTesting(log), false);
    });

    test('步骤101（脚本异常退出）应被标记为错误', () {
      final log = {
        'type': 1,
        'step': 101,
        'date': 1750407947586,
      };

      // 预期结果：是错误日志
      expect(isErrorLogForTesting(log), true);
    });

    test('未知步骤应被标记为错误', () {
      final log = {
        'type': 1,
        'step': 999, // 未知步骤
        'date': 1750407947586,
      };

      // 预期结果：是错误日志
      expect(isErrorLogForTesting(log), true);
    });

    test('包含错误代码的非步骤100日志应被标记为错误', () {
      final log = {
        'type': 3,
        'info': 'err',
        'date': 1750407947586,
        'args': {'code': 300}
      };

      // 预期结果：是错误日志
      expect(isErrorLogForTesting(log), true);
    });

    test('正常的步骤日志不应被标记为错误', () {
      final log = {
        'type': 1,
        'step': 1, // 任务启动
        'date': 1750407947586,
      };

      // 预期结果：不是错误日志
      expect(isErrorLogForTesting(log), false);
    });

    test('设备信息日志不应被标记为错误', () {
      final log = {
        'type': 0,
        'info': {
          'appVersion': '1.2.1',
          'scriptVersion': '1.5.22'
        }
      };

      // 预期结果：不是错误日志
      expect(isErrorLogForTesting(log), false);
    });
  });
}

// 测试辅助函数 - 模拟错误检测逻辑
bool isErrorLogForTesting(Map<String, dynamic> log) {
  final type = log['type']?.toString();
  
  // 设备信息、步骤、指令类型的错误判断
  if (type == '1') { // 步骤
    final step = log['step']?.toString();
    
    // 从映射表中获取描述（这里简化处理）
    const stepToText = {
      "1": "任务启动",
      "100": "脚本完成退出",
      "101": "脚本异常退出",
    };
    
    final description = stepToText[step];
    
    // 未知步骤视为错误
    if (description == null) return true;
    
    // 步骤101（脚本异常退出）明确标记为错误
    if (step == '101') return true;
    
    // 步骤100（脚本完成退出）不是错误，即使包含错误代码也是正常的退出状态码
    if (step == '100') return false;
    
  } else if (type == '2') { // 指令
    // 简化处理
    return false;
    
  } else if (type == '3') { // 日志
    final info = log['info']?.toString();
    
    // 明确标记为错误的日志
    if (info == 'err') return true;
    
  } else if (type != '0') { // 除了设备信息外，未知类型都视为错误
    return true;
  }
  
  // 检查是否有错误代码（但排除步骤100的情况）
  if (log.containsKey('args') && log['args'] is Map && log['args'].containsKey('code')) {
    // 如果是步骤类型且是步骤100（脚本完成退出），则不视为错误
    if (type == '1' && log['step']?.toString() == '100') {
      return false;
    }
    return true;
  }
  
  return false;
}
