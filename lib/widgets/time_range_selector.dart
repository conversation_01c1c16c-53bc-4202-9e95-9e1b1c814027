import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:omni_datetime_picker/omni_datetime_picker.dart';

class TimeRangeSelector extends StatelessWidget {
  const TimeRangeSelector({
    super.key,
    required this.startTime,
    required this.endTime,
    required this.onTimeRangeSelected,
  });

  final DateTime startTime;
  final DateTime endTime;
  final Function(DateTime, DateTime) onTimeRangeSelected;

  String _formatDateTime(DateTime dt) {
    return DateFormat('yy-MM-dd HH:mm:ss').format(dt);
  }

  Future<void> _showOmniPicker(BuildContext context) async {
    final List<DateTime>? dateTimeList = await showOmniDateTimeRangePicker(
      context: context,
      startInitialDate: startTime,
      startFirstDate: DateTime(2020),
      startLastDate: endTime.add(const Duration(days: 365)),
      endInitialDate: endTime,
      endFirstDate: startTime,
      endLastDate: DateTime.now().add(const Duration(days: 365)),
      is24HourMode: true,
      isShowSeconds: true,
      minutesInterval: 1,
      secondsInterval: 1,
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      constraints: const BoxConstraints(
        maxWidth: 350,
        maxHeight: 650,
      ),
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1.drive(
            Tween(
              begin: 0,
              end: 1,
            ),
          ),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      barrierDismissible: true,
    );

    if (dateTimeList != null && dateTimeList.length == 2) {
      onTimeRangeSelected(dateTimeList[0], dateTimeList[1]);
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _showOmniPicker(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.calendar_today, size: 18.0, color: Colors.blueAccent),
            const SizedBox(width: 8.0),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'From: ${_formatDateTime(startTime)}',
                  style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12, color: Colors.black87),
                ),
                Text(
                  'To:   ${_formatDateTime(endTime)}',
                  style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12, color: Colors.black87),
                ),
              ],
            ),
            const SizedBox(width: 8.0),
            const Icon(Icons.arrow_drop_down, color: Colors.black54),
          ],
        ),
      ),
    );
  }
} 