import 'package:flutter/material.dart';
import 'package:tpa_log_reader/models/log_entry.dart';
import 'package:tpa_log_reader/widgets/message_content_view.dart';
import 'package:tpa_log_reader/utils/formatters.dart';

class LogDetailPage extends StatefulWidget {
  final LogEntry logEntry;

  const LogDetailPage({
    super.key,
    required this.logEntry,
  });

  @override
  State<LogDetailPage> createState() => _LogDetailPageState();
}

class _LogDetailPageState extends State<LogDetailPage> {
  List<Map<String, dynamic>>? _logEntries;
  List<Map<String, dynamic>>? _filteredLogEntries;
  
  // 日志类型过滤
  String? _selectedLogType;
  final List<String> _logTypes = [
    '全部类型',
    '普通日志',
    '错误日志'
  ];
  
  // 日志排序相关状态
  bool _isReversed = false;

  @override
  void initState() {
    super.initState();
    _selectedLogType = '全部类型';
    _parseLogEntry();
  }

  void _parseLogEntry() {
    // 将单个日志条目转换为MessageContentView可以处理的格式
    final List<Map<String, dynamic>> entries = [];
    
    // 添加主要日志条目
    final mainEntry = {
      'timestamp': widget.logEntry.timestamp,
      'message': widget.logEntry.message,
      'level': widget.logEntry.level,
      'domain': widget.logEntry.domain,
      'parsedMessage': widget.logEntry.parsedMessage,
      'type': 'main',
    };
    entries.add(mainEntry);

    // 如果有解析的字段，为每个字段创建一个条目
    if (widget.logEntry.parsedMessage.isNotEmpty) {
      widget.logEntry.parsedMessage.forEach((key, value) {
        entries.add({
          'timestamp': widget.logEntry.timestamp,
          'message': '$key: $value',
          'level': 'info',
          'domain': 'parsed_field',
          'type': 'field',
          'field_key': key,
          'field_value': value,
        });
      });
    }
    
    setState(() {
      _logEntries = entries;
      _filteredLogEntries = entries;
    });
    
    _filterLogs();
  }

  // 判断日志是否为错误日志
  bool _isErrorLog(Map<String, dynamic> log) {
    final level = log['level']?.toString().toLowerCase();
    final message = log['message']?.toString().toLowerCase() ?? '';
    
    // 根据日志级别判断
    if (level == 'error' || level == 'err' || level == 'fatal') {
      return true;
    }
    
    // 根据消息内容判断
    if (message.contains('error') || 
        message.contains('exception') || 
        message.contains('failed') ||
        message.contains('错误')) {
      return true;
    }
    
    return false;
  }

  // 过滤日志
  void _filterLogs() {
    if (_logEntries == null) return;
    
    setState(() {
      if (_selectedLogType == null || _selectedLogType == '全部类型') {
        _filteredLogEntries = _logEntries;
      } else {
        _filteredLogEntries = _logEntries!.where((log) {
          switch (_selectedLogType) {
            case '错误日志':
              return _isErrorLog(log);
            case '普通日志':
              return !_isErrorLog(log);
            default:
              return true;
          }
        }).toList();
      }
      
      // 应用排序
      if (_isReversed && _filteredLogEntries != null) {
        _filteredLogEntries = _filteredLogEntries!.reversed.toList();
      }
    });
  }

  // 切换日志排序顺序
  void _toggleLogOrder() {
    setState(() {
      _isReversed = !_isReversed;
      if (_filteredLogEntries != null) {
        _filteredLogEntries = _filteredLogEntries!.reversed.toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final timeData = formatTimeForTimeline(widget.logEntry.timestamp);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('日志详情 - ${timeData['date']} ${timeData['time']}'),
        actions: [
          IconButton(
            icon: Icon(_isReversed ? Icons.arrow_upward : Icons.arrow_downward),
            tooltip: _isReversed ? '最新在上' : '最旧在上',
            onPressed: _toggleLogOrder,
          ),
        ],
      ),
      backgroundColor: const Color(0xFFF8F9FA),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_logEntries == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 筛选卡片
          _buildFiltersCard(),
          const SizedBox(height: 16),
          // 日志概要信息卡片
          _buildSummaryCard(),
          const SizedBox(height: 16),
          // 日志内容
          _buildLogsView(),
        ],
      ),
    );
  }

  Widget _buildFiltersCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // 日志类型过滤
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedLogType,
                items: _logTypes.map((type) => 
                  DropdownMenuItem(value: type, child: Text(type))
                ).toList(),
                onChanged: (type) {
                  setState(() {
                    _selectedLogType = type;
                  });
                  _filterLogs();
                },
                decoration: const InputDecoration(
                  labelText: '日志类型',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    final timeData = formatTimeForTimeline(widget.logEntry.timestamp);
    final totalLogs = _logEntries?.length ?? 0;
    final errorLogs = _logEntries?.where(_isErrorLog).length ?? 0;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('日志概要', style: Theme.of(context).textTheme.titleLarge),
            const Divider(height: 24),
            Wrap(
              spacing: 24.0,
              runSpacing: 12.0,
              children: [
                _buildInfoItem('时间', '${timeData['date']} ${timeData['time']}'),
                _buildInfoItem('来源', widget.logEntry.domain),
                _buildInfoItem('级别', widget.logEntry.level),
                _buildInfoItem('总数据', '$totalLogs 条'),
                _buildInfoItem('错误数据', '$errorLogs 条'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return RichText(
      text: TextSpan(
        style: Theme.of(context).textTheme.bodyMedium,
        children: [
          TextSpan(
            text: '$label: ',
            style: const TextStyle(color: Colors.grey),
          ),
          TextSpan(
            text: value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildLogsView() {
    if (_filteredLogEntries == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: Center(child: Text('没有可显示的日志。')),
        ),
      );
    }

    return Card(
      child: Column(
        children: [
          // 日志统计信息
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                const Icon(Icons.info_outline, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '共 ${_filteredLogEntries!.length}/${_logEntries!.length} 条日志',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          // 日志内容
          MessageContentView(
            content: _filteredLogEntries!,
          ),
        ],
      ),
    );
  }
}
