# TPA Log Reader - 发布检查清单

## 📋 构建前检查

### 环境验证
- [ ] Flutter SDK 版本 >= 3.4.1
- [ ] Dart SDK 版本 >= 3.4.1
- [ ] Android SDK 已正确配置
- [ ] Xcode 已安装并更新到最新版本 (macOS)
- [ ] Homebrew 已安装 (macOS)

### 代码质量检查
- [ ] 所有测试用例通过: `flutter test`
- [ ] 代码格式化: `dart format .`
- [ ] 静态分析无错误: `flutter analyze`
- [ ] 依赖项已更新: `flutter pub get`

### 版本信息更新
- [ ] `pubspec.yaml` 中的版本号已更新
- [ ] `RELEASE_NOTES.md` 已更新
- [ ] `README.md` 中的版本信息已更新
- [ ] 构建脚本中的版本号已更新

## 🚀 构建过程检查

### 自动化构建
- [ ] 运行构建脚本: `./scripts/build_all.sh`
- [ ] 构建过程无错误
- [ ] 所有目标平台构建成功

### 构建产物验证
- [ ] Android APK 文件存在: `build/app/outputs/flutter-apk/app-release.apk`
- [ ] macOS App 文件存在: `build/macos/Build/Products/Release/tpa_log_reader.app`
- [ ] macOS DMG 文件存在: `build/TPA-Log-Reader-v1.1.0.dmg`

### 文件大小检查
- [ ] Android APK 大小合理 (~24MB)
- [ ] macOS App 大小合理 (~55MB)
- [ ] macOS DMG 大小合理 (~177MB)

## 🧪 功能测试

### Android 测试
- [ ] APK 可以正常安装
- [ ] 应用可以正常启动
- [ ] 本地日志分析功能正常
- [ ] Kibana 日志查询功能正常
- [ ] 搜索功能正常工作
- [ ] 时间轴显示正确
- [ ] 详情页面导航正常

### macOS 测试
- [ ] DMG 可以正常挂载
- [ ] 应用可以拖拽安装
- [ ] 应用可以正常启动
- [ ] 本地日志分析功能正常
- [ ] Kibana 日志查询功能正常
- [ ] 搜索功能正常工作
- [ ] 时间轴显示正确
- [ ] 详情页面导航正常
- [ ] 响应式布局在不同窗口大小下正常

### 跨平台一致性
- [ ] 功能在两个平台上表现一致
- [ ] UI 布局在两个平台上适配良好
- [ ] 数据处理逻辑一致
- [ ] 错误处理行为一致

## 📄 文档检查

### 用户文档
- [ ] README.md 内容完整且准确
- [ ] 安装指南 (INSTALLATION.md) 已更新
- [ ] 构建指南 (BUILD_GUIDE.md) 已更新
- [ ] 发布说明 (RELEASE_NOTES.md) 已完成

### 技术文档
- [ ] API 文档已更新 (如适用)
- [ ] 架构文档已更新
- [ ] 故障排除指南已更新

## 🔒 安全检查

### 代码安全
- [ ] 敏感信息已移除 (API 密钥、密码等)
- [ ] 调试代码已清理
- [ ] 生产环境配置已应用

### 构建安全
- [ ] 构建环境干净
- [ ] 依赖项来源可信
- [ ] 构建产物完整性验证

## 📦 分发准备

### 文件准备
- [ ] 构建产物已复制到分发目录
- [ ] 文件命名规范正确
- [ ] 文件权限设置正确

### 分发渠道
- [ ] 内部分发渠道已准备
- [ ] 用户通知已准备
- [ ] 下载链接已测试

### 回滚计划
- [ ] 上一版本备份已保留
- [ ] 回滚步骤已文档化
- [ ] 紧急联系方式已确认

## ✅ 发布后验证

### 用户反馈
- [ ] 监控用户安装情况
- [ ] 收集用户反馈
- [ ] 跟踪崩溃报告

### 性能监控
- [ ] 应用启动时间正常
- [ ] 内存使用合理
- [ ] 网络请求正常

### 问题跟踪
- [ ] 已知问题已记录
- [ ] 修复计划已制定
- [ ] 下一版本规划已开始

## 🚨 紧急情况处理

### 严重问题发现
- [ ] 立即停止分发
- [ ] 评估问题影响范围
- [ ] 准备热修复或回滚
- [ ] 通知受影响用户

### 联系信息
- **技术负责人**: [联系方式]
- **项目经理**: [联系方式]
- **紧急联系**: [联系方式]

---

## 📝 发布签名

**构建负责人**: _________________ 日期: _________

**测试负责人**: _________________ 日期: _________

**项目负责人**: _________________ 日期: _________

---

*此检查清单确保每次发布都经过完整的质量验证流程*
