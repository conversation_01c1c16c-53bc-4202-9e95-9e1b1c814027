import 'dart:convert';
import 'package:flutter/foundation.dart';

@immutable
class HistoryItem {
  final String path;
  final String fileName;
  final int lastAccessed;

  const HistoryItem({
    required this.path,
    required this.fileName,
    required this.lastAccessed,
  });

  Map<String, dynamic> toMap() {
    return {
      'path': path,
      'fileName': fileName,
      'lastAccessed': lastAccessed,
    };
  }

  factory HistoryItem.fromMap(Map<String, dynamic> map) {
    return HistoryItem(
      path: map['path'] ?? '',
      fileName: map['fileName'] ?? '',
      lastAccessed: map['lastAccessed']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory HistoryItem.fromJson(String source) => HistoryItem.fromMap(json.decode(source));
} 